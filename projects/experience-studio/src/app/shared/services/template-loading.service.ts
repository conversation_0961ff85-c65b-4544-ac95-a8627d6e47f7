import { Injectable, inject, signal } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, map, switchMap, tap, timeout } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { environment } from '../../../environments/environment';
import { createLogger } from '../utils/logger';
import { ToastService } from './toast.service';
import { FileModel } from '../components/code-viewer/code-viewer.component';
import JSZip from 'jszip';
import { DestroyRef } from '@angular/core';


export interface TemplateLoadingState {
  isLoading: boolean;
  hasError: boolean;
  errorMessage: string | null;
  progress: 'idle' | 'downloading' | 'processing' | 'completed';
  templateFiles: FileModel[];
}

@Injectable({
  providedIn: 'root'
})
export class TemplateLoadingService {
  private readonly logger = createLogger('TemplateLoading');
  private readonly http = inject(HttpClient);
  private readonly toastService = inject(ToastService);

  // Angular 19+ Signals for reactive state management
  public readonly templateLoadingState = signal<TemplateLoadingState>({
    isLoading: false,
    hasError: false,
    errorMessage: null,
    progress: 'idle',
    templateFiles: []
  });

  // BehaviorSubject for compatibility with existing patterns
  private readonly templateFilesSubject = new BehaviorSubject<FileModel[]>([]);
  public readonly templateFiles$ = this.templateFilesSubject.asObservable();

  // Track current template loading operation
  private currentLoadingOperation: AbortController | null = null;
  private destroyRef = inject(DestroyRef);
  constructor() {
    this.destroyRef = inject(DestroyRef);
    this.logger.info('🏗️ TemplateLoadingService initialized with Angular 19+ patterns');
  }

  /**
   * Load seed project template based on framework and design library
   * REFACTORED: Now uses single direct API call instead of two-step process
   * @param framework The selected framework (e.g., 'angular', 'react', 'vue')
   * @param designLibrary The selected design library (e.g., 'tailwind', 'bootstrap', 'material')
   * @returns Observable<FileModel[]> Template files ready for Monaco Editor
   */
  loadTemplate(framework: string, designLibrary: string): Observable<FileModel[]> {
    console.log('🚀 Loading seed project template', { framework, designLibrary });

    // Cancel any existing loading operation
    this.cancelCurrentOperation();

    // Create new abort controller for this operation
    this.currentLoadingOperation = new AbortController();

    // Update loading state
    this.updateLoadingState({
      isLoading: true,
      hasError: false,
      errorMessage: null,
      progress: 'downloading',
      templateFiles: []
    });

    return this.downloadTemplateDirectly(framework, designLibrary).pipe(
      tap(() => {
        this.updateLoadingState({ progress: 'processing' });
      }),
      switchMap(zipBlob => this.processZipFile(zipBlob)),
      tap(files => {
        this.updateLoadingState({
          isLoading: false,
          progress: 'completed',
          templateFiles: files
        });
        this.templateFilesSubject.next(files);
        this.logger.info('✅ Template loading completed successfully', { fileCount: files.length });
        this.toastService.success(`Template loaded successfully with ${files.length} files`);
      }),
      catchError(error => {
        this.handleTemplateLoadingError(error);
        return throwError(() => error);
      }),
      takeUntilDestroyed(this.destroyRef)
    );
  }

  /**
   * Download template ZIP file directly from the API
   * Enhanced with comprehensive error handling and retry logic
   */
  private downloadTemplateDirectly(framework: string, designLibrary: string): Observable<Blob> {
    const params = new HttpParams()
      .set('framework', framework)
      .set('design_library', designLibrary);

    const url = `${environment.apiUrl}/download/template`;

    this.logger.info('⬇️ Downloading template ZIP file directly from API', {
      url,
      framework,
      designLibrary,
      timestamp: new Date().toISOString()
    });

    return this.http.get(url, {
      params,
      responseType: 'blob',
      // Add timeout and other options for better error handling
      headers: {
        'Accept': 'application/zip, application/octet-stream, */*'
      }
    }).pipe(
      timeout(60000), // 60 second timeout for large templates
      tap((blob: Blob) => {
        this.validateDownloadedBlob(blob, framework, designLibrary);
        this.logger.info('✅ Template ZIP downloaded successfully', {
          size: blob.size,
          type: blob.type,
          framework,
          designLibrary
        });
      }),
      catchError(error => {
        return this.handleDownloadError(error, framework, designLibrary, url);
      })
    );
  }

  /**
   * Validate downloaded blob for basic sanity checks
   */
  private validateDownloadedBlob(blob: Blob, framework: string, designLibrary: string): void {
    if (!blob) {
      throw new Error('No data received from template download');
    }

    if (blob.size === 0) {
      throw new Error('Empty template file received');
    }

    if (blob.size < 100) { // Suspiciously small for a template
      this.logger.warn('⚠️ Template file is very small', {
        size: blob.size,
        framework,
        designLibrary
      });
    }

    if (blob.size > 50 * 1024 * 1024) { // Larger than 50MB is suspicious
      this.logger.warn('⚠️ Template file is very large', {
        size: blob.size,
        framework,
        designLibrary
      });
    }

    // Check if blob type indicates an error response (like HTML error page)
    if (blob.type && (blob.type.includes('text/html') || blob.type.includes('text/plain'))) {
      this.logger.warn('⚠️ Template response may be an error page', {
        type: blob.type,
        size: blob.size
      });
    }
  }

  /**
   * Handle download errors with detailed error analysis and user-friendly messages
   */
  private handleDownloadError(error: any, framework: string, designLibrary: string, url: string): Observable<never> {
    this.logger.error('❌ Template download failed', {
      error: error.message || error,
      status: error.status,
      statusText: error.statusText,
      url,
      framework,
      designLibrary,
      timestamp: new Date().toISOString()
    });

    let userMessage = 'Failed to download template. ';
    let technicalDetails = '';

    // Analyze error type and provide specific guidance
    if (error.name === 'TimeoutError') {
      userMessage += 'The download took too long. Please try again.';
      technicalDetails = 'Download timeout after 60 seconds';
    } else if (error.status === 0) {
      userMessage += 'Please check your internet connection and try again.';
      technicalDetails = 'Network connection error (status 0)';
    } else if (error.status === 404) {
      userMessage += 'The requested template was not found. Please try a different framework or design library.';
      technicalDetails = `Template not found for ${framework}/${designLibrary}`;
    } else if (error.status === 400) {
      userMessage += 'Invalid template request. Please check your selections.';
      technicalDetails = 'Bad request - invalid framework or design library';
    } else if (error.status === 500) {
      userMessage += 'Server error occurred. Please try again later.';
      technicalDetails = 'Internal server error';
    } else if (error.status === 503) {
      userMessage += 'Service temporarily unavailable. Please try again later.';
      technicalDetails = 'Service unavailable';
    } else if (error.status >= 400 && error.status < 500) {
      userMessage += 'Request error. Please check your selections and try again.';
      technicalDetails = `Client error (${error.status})`;
    } else if (error.status >= 500) {
      userMessage += 'Server error. Please try again later.';
      technicalDetails = `Server error (${error.status})`;
    } else {
      userMessage += 'An unexpected error occurred. Please try again.';
      technicalDetails = error.message || 'Unknown error';
    }

    // Show user-friendly error message
    this.toastService.error(userMessage);

    // Create detailed error for logging and debugging
    const detailedError = new Error(`Template download failed: ${technicalDetails}`);
    (detailedError as any).originalError = error;
    (detailedError as any).context = { framework, designLibrary, url };

    return throwError(() => detailedError);
  }

  /**
   * Process ZIP file and convert to FileModel array
   * Enhanced with better file filtering, Monaco Editor compatibility, and comprehensive error handling
   */
  private async processZipFile(zipBlob: Blob): Promise<FileModel[]> {
    try {
      this.logger.info('📦 Processing ZIP file', { size: zipBlob.size });

      // Validate ZIP blob
      if (!zipBlob || zipBlob.size === 0) {
        throw new Error('Invalid or empty ZIP file received');
      }

      const zip = new JSZip();
      const zipContent = await zip.loadAsync(zipBlob, {
        checkCRC32: true, // Verify file integrity
        optimizedBinaryString: false,
        createFolders: false // We handle folder structure manually
      });

      const files: FileModel[] = [];
      const processedPaths = new Set<string>(); // Prevent duplicate processing
      let skippedFiles = 0;
      let processedFiles = 0;

      this.logger.info('📂 ZIP contains entries', {
        totalEntries: Object.keys(zipContent.files).length
      });

      // Process each file in the ZIP with enhanced error handling
      for (const [relativePath, zipEntry] of Object.entries(zipContent.files)) {
        try {
          // Enhanced filtering: Skip directories, hidden files, and system files
          if (this.shouldSkipFile(relativePath, zipEntry)) {
            skippedFiles++;
            this.logger.debug('⏭️ Skipped file', { path: relativePath, reason: 'filtered' });
            continue;
          }

          // Prevent duplicate processing
          if (processedPaths.has(relativePath)) {
            skippedFiles++;
            this.logger.debug('⏭️ Skipped duplicate file', { path: relativePath });
            continue;
          }

          // Extract file content with timeout protection
          const content = await this.extractFileContent(zipEntry, relativePath);

          if (content === null) {
            skippedFiles++;
            continue; // Skip files that couldn't be extracted
          }

          // Normalize file path for consistent handling
          const normalizedPath = this.normalizeFilePath(relativePath);
          const fileName = normalizedPath.split('/').pop() || normalizedPath;

          // Validate file content
          if (!this.isValidFileContent(content, normalizedPath)) {
            skippedFiles++;
            this.logger.warn('⚠️ Skipped file with invalid content', { path: normalizedPath });
            continue;
          }

          // Create FileModel compatible with Monaco Editor
          const fileModel: FileModel = {
            name: normalizedPath, // Full normalized path for Monaco Editor
            type: 'file',
            content: content,
            fileName: fileName // Just the filename for display
          };

          files.push(fileModel);
          processedPaths.add(relativePath);
          processedFiles++;

          this.logger.debug('📄 Processed file', {
            path: normalizedPath,
            fileName: fileName,
            size: content.length,
            encoding: this.detectFileEncoding(content)
          });

        } catch (fileError) {
          skippedFiles++;
          this.logger.warn('⚠️ Failed to process file in ZIP', {
            path: relativePath,
            error: fileError instanceof Error ? fileError.message : String(fileError),
            stack: fileError instanceof Error ? fileError.stack : undefined
          });
        }
      }

      // Validate that we have at least some files
      if (files.length === 0) {
        throw new Error('No valid files found in the template ZIP. The template may be corrupted or empty.');
      }

      // Sort files for consistent ordering (directories first, then files alphabetically)
      files.sort(this.compareFileModels);

      this.logger.info('✅ ZIP processing completed successfully', {
        totalFiles: files.length,
        processedFiles,
        skippedFiles,
        fileTypes: this.getFileTypesSummary(files),
        largestFile: this.getLargestFile(files),
        totalSize: files.reduce((sum, f) => sum + (f.content?.length || 0), 0)
      });

      return files;

    } catch (error) {
      this.logger.error('❌ Failed to process ZIP file', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        zipSize: zipBlob?.size || 0
      });

      const errorMessage = error instanceof Error && error.message.includes('template')
        ? error.message
        : 'Failed to process template files. The ZIP file may be corrupted or invalid.';

      this.toastService.error(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * Enhanced file filtering logic with comprehensive exclusion rules
   */
  private shouldSkipFile(relativePath: string, zipEntry: any): boolean {
    // Skip directories
    if (zipEntry.dir) {
      return true;
    }

    // Skip empty paths
    if (!relativePath.trim()) {
      return true;
    }

    // Skip hidden files and system files
    if (relativePath.startsWith('.') ||
        relativePath.includes('/.') ||
        relativePath.includes('__MACOSX') ||
        relativePath.includes('.DS_Store') ||
        relativePath.includes('Thumbs.db') ||
        relativePath.includes('desktop.ini')) {
      return true;
    }

    // Skip common build/cache directories
    const skipDirectories = [
      'node_modules/', 'dist/', 'build/', '.git/', '.svn/', '.hg/',
      'coverage/', '.nyc_output/', '.cache/', 'tmp/', 'temp/',
      '.vscode/', '.idea/', '.vs/', 'bin/', 'obj/'
    ];

    if (skipDirectories.some(dir => relativePath.includes(dir))) {
      return true;
    }

    // Skip binary and non-text files that shouldn't be in templates
    const skipExtensions = [
      '.exe', '.dll', '.so', '.dylib', '.bin', '.obj', '.o',
      '.zip', '.tar', '.gz', '.rar', '.7z',
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.webp',
      '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv',
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'
    ];

    const fileExtension = relativePath.toLowerCase().split('.').pop();
    if (fileExtension && skipExtensions.includes(`.${fileExtension}`)) {
      return true;
    }

    // Skip very large files (> 1MB) that are likely not source code
    if (zipEntry.uncompressedSize && zipEntry.uncompressedSize > 1024 * 1024) {
      this.logger.warn('⚠️ Skipping large file', {
        path: relativePath,
        size: zipEntry.uncompressedSize
      });
      return true;
    }

    return false;
  }

  /**
   * Extract file content with timeout and error handling
   */
  private async extractFileContent(zipEntry: any, relativePath: string): Promise<string | null> {
    try {
      // Set a reasonable timeout for file extraction (30 seconds)
      const extractionPromise = zipEntry.async('string');
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('File extraction timeout')), 30000);
      });

      const content = await Promise.race([extractionPromise, timeoutPromise]);

      // Validate content is not null/undefined
      if (content === null || content === undefined) {
        this.logger.warn('⚠️ File content is null/undefined', { path: relativePath });
        return null;
      }

      return content;
    } catch (error) {
      this.logger.warn('⚠️ Failed to extract file content', {
        path: relativePath,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Normalize file path for consistent handling
   */
  private normalizeFilePath(path: string): string {
    return path
      .replace(/\\/g, '/') // Convert Windows paths to Unix style
      .replace(/\/+/g, '/') // Remove duplicate slashes
      .replace(/^\//, '') // Remove leading slash
      .trim();
  }

  /**
   * Validate file content for basic sanity checks
   */
  private isValidFileContent(content: string, filePath: string): boolean {
    // Check for null/undefined
    if (content === null || content === undefined) {
      return false;
    }

    // Allow empty files for certain types (like .gitkeep, empty configs)
    const allowEmptyExtensions = ['.gitkeep', '.gitignore', '.env', '.env.example'];
    const isAllowedEmpty = allowEmptyExtensions.some(ext => filePath.endsWith(ext));

    if (content.length === 0 && !isAllowedEmpty) {
      return false;
    }

    // Check for binary content in text files (basic heuristic)
    if (content.length > 0) {
      const nullBytes = (content.match(/\0/g) || []).length;
      const ratio = nullBytes / content.length;

      // If more than 1% null bytes, likely binary
      if (ratio > 0.01) {
        this.logger.warn('⚠️ File appears to be binary', { path: filePath, nullByteRatio: ratio });
        return false;
      }
    }

    return true;
  }

  /**
   * Detect file encoding for logging purposes
   */
  private detectFileEncoding(content: string): string {
    // Simple heuristic for encoding detection
    try {
      // Check for UTF-8 BOM
      if (content.charCodeAt(0) === 0xFEFF) {
        return 'UTF-8 with BOM';
      }

      // Check for common non-ASCII characters
      const hasNonAscii = /[^\x00-\x7F]/.test(content);
      return hasNonAscii ? 'UTF-8' : 'ASCII';
    } catch {
      return 'Unknown';
    }
  }

  /**
   * Compare function for sorting FileModel arrays
   */
  private compareFileModels(a: FileModel, b: FileModel): number {
    // Prioritize certain important files at the top
    const importantFiles = ['package.json', 'README.md', 'index.html', 'App.tsx', 'App.jsx', 'main.tsx', 'main.jsx'];
    const aImportant = importantFiles.indexOf(a.fileName || '');
    const bImportant = importantFiles.indexOf(b.fileName || '');

    if (aImportant !== -1 && bImportant !== -1) {
      return aImportant - bImportant;
    }
    if (aImportant !== -1) return -1;
    if (bImportant !== -1) return 1;

    // Then sort alphabetically by path
    const aPath = a.name || a.fileName || '';
    const bPath = b.name || b.fileName || '';
    return aPath.localeCompare(bPath);
  }

  /**
   * Get the largest file for logging purposes
   */
  private getLargestFile(files: FileModel[]): { name: string; size: number } | null {
    if (files.length === 0) return null;

    let largest = files[0];
    let largestSize = largest.content?.length || 0;

    for (const file of files) {
      const size = file.content?.length || 0;
      if (size > largestSize) {
        largest = file;
        largestSize = size;
      }
    }

    return {
      name: largest.name || largest.fileName || 'unknown',
      size: largestSize
    };
  }

  /**
   * Get summary of file types for logging with enhanced categorization
   */
  private getFileTypesSummary(files: FileModel[]): Record<string, number> {
    const summary: Record<string, number> = {};

    files.forEach(file => {
      const fileName = file.fileName || file.name || '';
      let extension = fileName.split('.').pop()?.toLowerCase() || 'no-extension';

      // Group similar extensions
      const extensionGroups: Record<string, string> = {
        'js': 'javascript',
        'jsx': 'javascript',
        'ts': 'typescript',
        'tsx': 'typescript',
        'css': 'styles',
        'scss': 'styles',
        'sass': 'styles',
        'less': 'styles',
        'html': 'markup',
        'htm': 'markup',
        'xml': 'markup',
        'json': 'config',
        'yaml': 'config',
        'yml': 'config',
        'toml': 'config',
        'ini': 'config',
        'md': 'documentation',
        'txt': 'documentation',
        'rst': 'documentation'
      };

      const category = extensionGroups[extension] || extension;
      summary[category] = (summary[category] || 0) + 1;
    });

    return summary;
  }

  /**
   * Update loading state using Angular Signals
   */
  private updateLoadingState(updates: Partial<TemplateLoadingState>): void {
    const currentState = this.templateLoadingState();
    this.templateLoadingState.set({ ...currentState, ...updates });
  }

  /**
   * Handle template loading errors
   */
  private handleTemplateLoadingError(error: any): void {
    this.logger.error('❌ Template loading failed', error);

    const errorMessage = error?.message || 'Failed to load template. Please try again.';

    this.updateLoadingState({
      isLoading: false,
      hasError: true,
      errorMessage: errorMessage,
      progress: 'idle'
    });

    // Show user-friendly error message
    // this.toastService.error(errorMessage);
  }

  /**
   * Cancel current loading operation
   */
  private cancelCurrentOperation(): void {
    if (this.currentLoadingOperation) {
      this.currentLoadingOperation.abort();
      this.currentLoadingOperation = null;
      this.logger.info('🚫 Cancelled previous template loading operation');
    }
  }

  /**
   * Reset template loading state
   */
  resetState(): void {
    this.cancelCurrentOperation();
    this.updateLoadingState({
      isLoading: false,
      hasError: false,
      errorMessage: null,
      progress: 'idle',
      templateFiles: []
    });
    this.templateFilesSubject.next([]);
    this.logger.info('🔄 Template loading state reset');
  }

  /**
   * Get current template files (for compatibility)
   */
  getCurrentTemplateFiles(): FileModel[] {
    return this.templateLoadingState().templateFiles;
  }

  /**
   * Check if template loading is in progress
   */
  isLoading(): boolean {
    return this.templateLoadingState().isLoading;
  }
}
