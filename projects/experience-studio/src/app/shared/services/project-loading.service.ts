import { Injectable, inject, DestroyRef } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { environment } from '../../../environments/environment';
import { UserSignatureService } from './user-signature.service';
import { ToastService } from './toast.service';
import { cacheHelpers } from '../interceptors/cache.interceptor';

// Project Loading Data Interfaces based on project-loading.json structure
export interface ProjectDetails {
  project_id: string;
  project_name: string;
  project_description: string;
  created_at: string;
  last_modified: string;
  created_by: string;
  project_state: string;
}

export interface ProjectSettings {
  project_id: string;
  device: string;
  framework: string;
  design_system: string;
  generation_type: string;
}

export interface RepositoryDetails {
  project_id: string;
  vcs_provider: string;
  clone_url: string;
  deployment_provider: string;
  deployed_url: string;
}

export interface ConversationMessage {
  message_id: string;
  conversation_id: string;
  message_type: 'user' | 'assistant' | 'capsule';
  content: string;
  ui_metadata: string | null;
  created_at: string;
}

export interface ProjectLoadingResponse {
  project_details: ProjectDetails;
  project_settings: ProjectSettings;
  repository_details: RepositoryDetails;
  conversation: ConversationMessage[];
  metadata?: any; // For first-cut generation data
}

// Template API Response Interface
export interface TemplateFile {
  path: string;
  content: string;
  type: string;
}

export interface TemplateResponse {
  status_code: number;
  files: TemplateFile[];
  commit_id: string;
}

@Injectable({
  providedIn: 'root'
})
export class ProjectLoadingService {
  private readonly http = inject(HttpClient);
  private readonly userSignatureService = inject(UserSignatureService);
  private readonly toastService = inject(ToastService);
  private readonly destroyRef = inject(DestroyRef);
  
  private readonly apiUrl = environment.apiUrl;

  /**
   * Load project data by project ID
   * @param projectId The project ID to load
   * @param userSignature Optional user signature
   * @returns Observable with project loading response
   */
  loadProjectData(projectId: string, userSignature?: string): Observable<ProjectLoadingResponse> {
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();
    
    const params = new HttpParams().set('user_signature', signature);
    
    // Set cache for project loading data (5 minutes)
    const context = cacheHelpers.setMaxAge(5 * 60 * 1000);

    return this.http.get<ProjectLoadingResponse>(`${this.apiUrl}/project/${projectId}`, {
      params,
      context
    }).pipe(
      tap(response => {
        console.log('Project data loaded successfully:', response);
      }),
      catchError(error => {
        const errorMessage = `Failed to load project data: ${error.message || 'Unknown error'}`;
        this.toastService.error(errorMessage);
        console.error('Project loading error:', error);
        return throwError(() => new Error(errorMessage));
      }),
      takeUntilDestroyed(this.destroyRef)
    );
  }

  /**
   * Get project version data by project ID and commit ID
   * @param projectId The project ID
   * @param commitId The commit ID to fetch version for
   * @param userSignature Optional user signature
   * @returns Observable with template response
   */
  getProjectVersion(projectId: string, commitId: string, userSignature?: string): Observable<TemplateResponse> {
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    const payload = {
      project_id: projectId,
      commit_id: commitId
    };

    const params = new HttpParams().set('user_signature', signature);

    // Set cache for version data (10 minutes)
    const context = cacheHelpers.setMaxAge(10 * 60 * 1000);

    return this.http.post<TemplateResponse>(`${this.apiUrl}/build/version`, payload, {
      params,
      context
    }).pipe(
      tap(response => {
        console.log('Project version data loaded successfully:', response);
      }),
      catchError(error => {
        const errorMessage = `Failed to load project version data: ${error.message || 'Unknown error'}`;
        this.toastService.error(errorMessage);
        console.error('Project version loading error:', error);
        return throwError(() => new Error(errorMessage));
      }),
      takeUntilDestroyed(this.destroyRef)
    );
  }

  /**
   * Parse conversation messages to extract capsule commit IDs
   * @param conversation Array of conversation messages
   * @returns Array of commit IDs from capsule messages
   */
  extractCommitIdsFromConversation(conversation: ConversationMessage[]): string[] {
    return conversation
      .filter(message => message.message_type === 'capsule')
      .map(message => message.content)
      .filter(content => content && content.length > 0);
  }

  /**
   * Clean deployed URL by removing ANSI escape codes and extra characters
   * @param deployedUrl Raw deployed URL from API
   * @returns Cleaned URL
   */
  cleanDeployedUrl(deployedUrl: string): string {
    if (!deployedUrl) return '';
    
    // Remove ANSI escape codes like [0m
    return deployedUrl.replace(/\[[0-9;]*m/g, '').trim();
  }

  /**
   * Parse UI metadata from assistant messages
   * @param uiMetadata Raw UI metadata string
   * @returns Parsed metadata object or null
   */
  parseUIMetadata(uiMetadata: string | null): any {
    if (!uiMetadata) return null;
    
    try {
      return JSON.parse(uiMetadata);
    } catch (error) {
      console.warn('Failed to parse UI metadata:', error);
      return null;
    }
  }

  /**
   * Validate project loading response
   * @param response Project loading response to validate
   * @returns Boolean indicating if response is valid
   */
  validateProjectLoadingResponse(response: ProjectLoadingResponse): boolean {
    return !!(
      response &&
      response.project_details &&
      response.project_details.project_id &&
      response.project_settings &&
      response.repository_details
    );
  }
}
