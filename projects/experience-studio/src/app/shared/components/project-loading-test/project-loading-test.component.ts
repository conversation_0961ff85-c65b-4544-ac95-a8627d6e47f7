import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProjectNavigationService } from '../../services/project-navigation.service';
import { ProjectLoadingService } from '../../services/project-loading.service';

/**
 * Test component to verify project loading flow
 * This component can be used to test the recent project click functionality
 */
@Component({
  selector: 'app-project-loading-test',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="project-loading-test">
      <h2>Project Loading Test</h2>
      
      <div class="test-section">
        <h3>Test Navigation</h3>
        <button 
          class="test-button" 
          (click)="testNavigation()"
          [disabled]="isLoading">
          Test Navigate to Project
        </button>
      </div>

      <div class="test-section">
        <h3>Test API Call</h3>
        <button 
          class="test-button" 
          (click)="testApiCall()"
          [disabled]="isLoading">
          Test Project Loading API
        </button>
      </div>

      <div class="test-section" *ngIf="testResults.length > 0">
        <h3>Test Results</h3>
        <div class="results">
          <div 
            *ngFor="let result of testResults" 
            [class]="'result-item ' + result.type">
            <strong>{{result.timestamp}}</strong>: {{result.message}}
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .project-loading-test {
      padding: 20px;
      max-width: 600px;
      margin: 0 auto;
    }

    .test-section {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }

    .test-button {
      padding: 10px 20px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    .test-button:hover:not(:disabled) {
      background-color: #0056b3;
    }

    .test-button:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }

    .results {
      max-height: 300px;
      overflow-y: auto;
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
    }

    .result-item {
      padding: 5px 0;
      border-bottom: 1px solid #eee;
    }

    .result-item.success {
      color: #28a745;
    }

    .result-item.error {
      color: #dc3545;
    }

    .result-item.info {
      color: #17a2b8;
    }
  `]
})
export class ProjectLoadingTestComponent {
  private readonly projectNavigationService = inject(ProjectNavigationService);
  private readonly projectLoadingService = inject(ProjectLoadingService);

  isLoading = false;
  testResults: Array<{
    timestamp: string;
    message: string;
    type: 'success' | 'error' | 'info';
  }> = [];

  // Test project ID from your sample data
  private readonly TEST_PROJECT_ID = 'fa9dbd20-6a09-45d3-bd10-e63ed4551393';

  /**
   * Test the navigation functionality
   */
  testNavigation(): void {
    this.addResult('Testing navigation to project...', 'info');
    
    try {
      this.projectNavigationService.navigateToProject(
        this.TEST_PROJECT_ID, 
        'MindfulPrompts Test'
      );
      this.addResult('Navigation initiated successfully', 'success');
    } catch (error) {
      this.addResult(`Navigation failed: ${error}`, 'error');
    }
  }

  /**
   * Test the API call functionality
   */
  testApiCall(): void {
    this.isLoading = true;
    this.addResult('Testing project loading API call...', 'info');

    this.projectLoadingService.loadProjectData(this.TEST_PROJECT_ID)
      .subscribe({
        next: (response) => {
          this.addResult('API call successful', 'success');
          this.addResult(`Project: ${response.project_details.project_name}`, 'info');
          this.addResult(`Framework: ${response.project_settings.framework}`, 'info');
          this.isLoading = false;
        },
        error: (error) => {
          this.addResult(`API call failed: ${error.message}`, 'error');
          this.isLoading = false;
        }
      });
  }

  /**
   * Add a result to the test results array
   */
  private addResult(message: string, type: 'success' | 'error' | 'info'): void {
    const timestamp = new Date().toLocaleTimeString();
    this.testResults.unshift({ timestamp, message, type });
    
    // Keep only the last 20 results
    if (this.testResults.length > 20) {
      this.testResults = this.testResults.slice(0, 20);
    }
  }
}
