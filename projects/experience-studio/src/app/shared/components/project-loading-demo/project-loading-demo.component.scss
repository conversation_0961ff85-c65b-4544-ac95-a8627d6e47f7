// Demo Header Styles
.demo-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h1 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .loading-state {
    .state-indicator {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-weight: 500;
      text-transform: uppercase;
      font-size: 0.8rem;
      letter-spacing: 0.5px;

      &.idle {
        background: rgba(255, 255, 255, 0.2);
      }

      &.loading {
        background: #ffc107;
        color: #212529;
        animation: pulse 1.5s infinite;
      }

      &.loaded {
        background: #28a745;
        color: white;
      }

      &.error {
        background: #dc3545;
        color: white;
      }
    }
  }
}

// Main container adjustments
.container {
  margin-top: 80px; // Account for fixed header
  height: calc(100vh - 80px);
}

// Custom header styles (matching code-window)
.custom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--awe-split-border-color, #e0e0e0);
  background: var(--awe-header-bg, #ffffff);

  .header-left {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .header-center {
    flex: 1;
    text-align: center;

    .project-name {
      font-weight: 600;
      font-size: 1rem;
      color: var(--awe-text-primary, #333);
    }
  }

  .header-right {
    .project-status {
      background: #28a745;
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }
  }
}

// Tab styles (matching code-window)
.tabs-container {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--awe-split-border-color, #e0e0e0);
  background: var(--awe-header-bg, #ffffff);

  .tab-buttons {
    display: flex;
    gap: 0.5rem;

    .tab-button {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border: none;
      background: transparent;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 0.9rem;
      color: var(--awe-text-secondary, #666);

      &:hover {
        background: var(--awe-hover-bg, #f5f5f5);
      }

      &.active {
        background: var(--awe-primary-bg, #007bff);
        color: white;
      }
    }
  }
}

// Preview View Styles
.preview-view {
  height: 100%;
  display: flex;
  flex-direction: column;

  .preview-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      border-bottom: 1px solid var(--awe-split-border-color, #e0e0e0);

      h3 {
        margin: 0;
        font-size: 1.1rem;
        color: var(--awe-text-primary, #333);
      }

      .preview-link {
        color: var(--awe-primary, #007bff);
        text-decoration: none;
        font-size: 0.9rem;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .preview-iframe {
      flex: 1;
      width: 100%;
      border: none;
      background: white;
    }
  }
}

// Code View Styles
.code-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Artifacts View Styles
.artifacts-view {
  height: 100%;
  display: flex;
  flex-direction: column;

  .artifacts-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .artifacts-header {
      padding: 1rem;
      border-bottom: 1px solid var(--awe-split-border-color, #e0e0e0);

      h3 {
        margin: 0;
        font-size: 1.1rem;
        color: var(--awe-text-primary, #333);
      }
    }

    .artifacts-content {
      flex: 1;
      overflow-y: auto;
      padding: 1rem;

      .artifact-section {
        margin-bottom: 2rem;

        h4 {
          margin: 0 0 1rem 0;
          font-size: 1rem;
          color: var(--awe-text-primary, #333);
          border-bottom: 1px solid var(--awe-split-border-color, #e0e0e0);
          padding-bottom: 0.5rem;
        }

        .no-regenerations {
          padding: 2rem;
          text-align: center;

          .no-regenerations-message {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            background: var(--awe-card-bg, #f8f9fa);
            border-radius: 8px;
            border: 1px solid var(--awe-split-border-color, #e0e0e0);

            .icon {
              font-size: 1.2rem;
            }

            .text {
              color: var(--awe-text-secondary, #666);
              font-style: italic;
            }
          }
        }

        .regeneration-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .regeneration-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1rem;
            background: var(--awe-primary-bg, #e3f2fd);
            border-radius: 6px;
            border: 1px solid var(--awe-primary, #007bff);
            margin-bottom: 0.5rem;

            .count {
              font-weight: 600;
              color: var(--awe-primary, #007bff);
            }

            .files-count {
              font-size: 0.9rem;
              color: var(--awe-text-secondary, #666);
            }
          }

          .regeneration-item {
            background: var(--awe-card-bg, #f8f9fa);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid var(--awe-split-border-color, #e0e0e0);

            &.has-files {
              border-left: 4px solid var(--awe-success, #28a745);
            }

            .regeneration-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 0.5rem;

              .regeneration-type {
                font-weight: 600;
                text-transform: capitalize;
                color: var(--awe-primary, #007bff);
              }

              .regeneration-time {
                font-size: 0.8rem;
                color: var(--awe-text-secondary, #666);
              }

              .files-badge {
                background: var(--awe-success, #28a745);
                color: white;
                padding: 0.25rem 0.5rem;
                border-radius: 12px;
                font-size: 0.7rem;
                font-weight: 500;
              }
            }

            .regeneration-content {
              color: var(--awe-text-primary, #333);
              line-height: 1.5;
              margin-bottom: 0.5rem;
            }

            .files-list {
              margin-top: 1rem;
              padding-top: 1rem;
              border-top: 1px solid var(--awe-split-border-color, #e0e0e0);

              .file-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.25rem 0;

                .file-name {
                  font-family: monospace;
                  background: white;
                  padding: 0.25rem 0.5rem;
                  border-radius: 4px;
                  font-size: 0.8rem;
                  border: 1px solid var(--awe-split-border-color, #e0e0e0);
                }

                .file-language {
                  color: var(--awe-text-secondary, #666);
                  font-size: 0.75rem;
                }
              }
            }
          }
        }

        .project-overview {
          margin-bottom: 2rem;

          h5 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--awe-primary, #007bff);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
          }

          .overview-content {
            background: var(--awe-card-bg, #f8f9fa);
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid var(--awe-split-border-color, #e0e0e0);

            pre {
              white-space: pre-wrap;
              word-wrap: break-word;
              font-family: inherit;
              font-size: 0.9rem;
              line-height: 1.6;
              color: var(--awe-text-primary, #333);
              margin: 0;
            }
          }
        }

        .log-entries {
          margin-top: 2rem;

          h5 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--awe-primary, #007bff);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
          }

          .log-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .log-item {
              background: var(--awe-card-bg, #f8f9fa);
              border-radius: 8px;
              padding: 1rem;
              border: 1px solid var(--awe-split-border-color, #e0e0e0);

              &.completed {
                border-left: 4px solid var(--awe-success, #28a745);
              }

              .log-header {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 0.5rem;

                .log-number {
                  background: var(--awe-primary, #007bff);
                  color: white;
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 0.8rem;
                  font-weight: 600;
                }

                .log-progress {
                  font-weight: 600;
                  color: var(--awe-text-primary, #333);
                }

                .log-status {
                  padding: 0.25rem 0.5rem;
                  border-radius: 12px;
                  font-size: 0.7rem;
                  font-weight: 500;
                  text-transform: uppercase;

                  &.completed {
                    background: var(--awe-success, #28a745);
                    color: white;
                  }

                  &.in_progress {
                    background: var(--awe-warning, #ffc107);
                    color: white;
                  }

                  &.failed {
                    background: var(--awe-danger, #dc3545);
                    color: white;
                  }
                }
              }

              .log-description {
                color: var(--awe-text-secondary, #666);
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
                font-style: italic;
              }

              .log-content {
                color: var(--awe-text-primary, #333);
                font-size: 0.85rem;
                font-family: monospace;
                background: rgba(0, 0, 0, 0.05);
                padding: 0.5rem;
                border-radius: 4px;
                white-space: pre-wrap;
              }
            }
          }
        }

        .no-artifacts {
          padding: 2rem;
          text-align: center;

          .no-artifacts-message {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            background: var(--awe-card-bg, #f8f9fa);
            border-radius: 8px;
            border: 1px solid var(--awe-split-border-color, #e0e0e0);

            .icon {
              font-size: 1.2rem;
            }

            .text {
              color: var(--awe-text-secondary, #666);
              font-style: italic;
            }
          }
        }

        .artifacts-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .artifacts-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1rem;
            background: var(--awe-primary-bg, #e3f2fd);
            border-radius: 6px;
            border: 1px solid var(--awe-primary, #007bff);
            margin-bottom: 0.5rem;

            .count {
              font-weight: 600;
              color: var(--awe-primary, #007bff);
            }

            .types {
              font-size: 0.9rem;
              color: var(--awe-text-secondary, #666);
            }
          }

          .artifact-item {
            background: var(--awe-card-bg, #f8f9fa);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid var(--awe-split-border-color, #e0e0e0);
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              border-color: var(--awe-primary, #007bff);
              box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
            }

            &.selected {
              border-color: var(--awe-primary, #007bff);
              background: var(--awe-primary-bg, #e3f2fd);
            }

            .artifact-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 0.5rem;

              .artifact-name {
                font-weight: 600;
                color: var(--awe-text-primary, #333);
              }

              .artifact-type {
                padding: 0.25rem 0.5rem;
                border-radius: 12px;
                font-size: 0.7rem;
                font-weight: 500;
                text-transform: uppercase;

                &.type-json {
                  background: var(--awe-warning, #ffc107);
                  color: white;
                }

                &.type-ref_code {
                  background: var(--awe-success, #28a745);
                  color: white;
                }

                &.type-text {
                  background: var(--awe-info, #17a2b8);
                  color: white;
                }

                &.type-artifact {
                  background: var(--awe-secondary, #6c757d);
                  color: white;
                }

                &.type-files {
                  background: var(--awe-primary, #007bff);
                  color: white;
                }

                &.type-repository {
                  background: var(--awe-dark, #343a40);
                  color: white;
                }
              }
            }

            .artifact-source {
              color: var(--awe-text-secondary, #666);
              font-size: 0.9rem;
              margin-bottom: 0.25rem;
            }

            .artifact-progress {
              color: var(--awe-text-secondary, #666);
              font-size: 0.8rem;
              font-style: italic;
            }
          }
        }

        .artifact-viewer {
          background: var(--awe-card-bg, #f8f9fa);
          border-radius: 8px;
          border: 1px solid var(--awe-split-border-color, #e0e0e0);
          overflow: hidden;

          .artifact-viewer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--awe-primary-bg, #e3f2fd);
            border-bottom: 1px solid var(--awe-split-border-color, #e0e0e0);

            .artifact-viewer-name {
              font-weight: 600;
              color: var(--awe-primary, #007bff);
            }

            .artifact-viewer-type {
              padding: 0.25rem 0.5rem;
              background: var(--awe-primary, #007bff);
              color: white;
              border-radius: 12px;
              font-size: 0.7rem;
              font-weight: 500;
              text-transform: uppercase;
            }
          }

          .artifact-content {
            padding: 1rem;
            max-height: 400px;
            overflow-y: auto;

            .json-content pre {
              background: #f8f9fa;
              padding: 1rem;
              border-radius: 4px;
              font-family: 'Courier New', monospace;
              font-size: 0.8rem;
              line-height: 1.4;
              overflow-x: auto;
              white-space: pre-wrap;
            }

            .ref-code-content .ref-link {
              color: var(--awe-primary, #007bff);
              text-decoration: none;
              word-break: break-all;

              &:hover {
                text-decoration: underline;
              }
            }

            .text-content {
              .text-content-formatted {
                line-height: 1.6;
                color: var(--awe-text-primary, #333);
                white-space: pre-wrap;
                word-wrap: break-word;
              }
            }

            .files-content {
              .files-summary {
                font-weight: 600;
                color: var(--awe-primary, #007bff);
                margin-bottom: 1rem;
                padding: 0.5rem;
                background: var(--awe-primary-bg, #e3f2fd);
                border-radius: 4px;
              }

              .file-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.5rem;
                margin-bottom: 0.5rem;
                background: #f8f9fa;
                border-radius: 4px;
                border: 1px solid var(--awe-split-border-color, #e0e0e0);

                .file-name {
                  font-family: monospace;
                  font-weight: 600;
                  color: var(--awe-text-primary, #333);
                }

                .file-size {
                  color: var(--awe-text-secondary, #666);
                  font-size: 0.8rem;
                }
              }
            }

            .repository-content {
              .repo-info {
                display: flex;
                flex-direction: column;
                gap: 0.75rem;

                > div {
                  display: flex;
                  align-items: center;
                  gap: 0.5rem;

                  strong {
                    min-width: 100px;
                    color: var(--awe-text-primary, #333);
                  }

                  a {
                    color: var(--awe-primary, #007bff);
                    text-decoration: none;
                    word-break: break-all;

                    &:hover {
                      text-decoration: underline;
                    }
                  }
                }
              }
            }

            .default-content pre {
              background: #f8f9fa;
              padding: 1rem;
              border-radius: 4px;
              font-family: 'Courier New', monospace;
              font-size: 0.8rem;
              white-space: pre-wrap;
            }
          }
        }

        .version-list {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          .version-item {
            background: var(--awe-card-bg, #f8f9fa);
            border-radius: 6px;
            padding: 0.75rem;
            border: 1px solid var(--awe-split-border-color, #e0e0e0);

            .version-header {
              display: flex;
              align-items: center;
              gap: 1rem;
              margin-bottom: 0.25rem;

              .version-number {
                font-weight: 600;
                color: var(--awe-primary, #007bff);
              }

              .commit-id {
                font-family: monospace;
                background: white;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.8rem;
                border: 1px solid var(--awe-split-border-color, #e0e0e0);
              }
            }

            .version-description {
              color: var(--awe-text-secondary, #666);
              font-size: 0.9rem;
            }
          }
        }

        .repo-details {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .repo-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;

            label {
              font-weight: 600;
              color: var(--awe-text-secondary, #666);
              font-size: 0.9rem;
            }

            code {
              background: var(--awe-code-bg, #f8f9fa);
              padding: 0.5rem;
              border-radius: 4px;
              font-family: monospace;
              font-size: 0.8rem;
              border: 1px solid var(--awe-split-border-color, #e0e0e0);
              word-break: break-all;
            }

            span {
              color: var(--awe-text-primary, #333);
              padding: 0.5rem;
              background: var(--awe-card-bg, #f8f9fa);
              border-radius: 4px;
              border: 1px solid var(--awe-split-border-color, #e0e0e0);
            }
          }
        }
      }
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-header {
    padding: 0.75rem 1rem;

    h1 {
      font-size: 1.2rem;
    }
  }

  .container {
    margin-top: 70px;
    height: calc(100vh - 70px);
  }

  .tabs-container .tab-buttons {
    flex-wrap: wrap;
  }
}
