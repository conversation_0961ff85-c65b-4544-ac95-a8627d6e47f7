import { Component, OnInit, ChangeDetectionStrategy, inject, DestroyRef, ViewChild, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, timer } from 'rxjs';

import { ProjectLoadingResponse } from '../../services/project-loading.service';
import { ProjectLoadingParser } from '../../utils/project-loading-parser.util';
import { ParsedProjectData, ProjectLoadingState } from '../../interfaces/project-loading.interfaces';
import { StepperStateService } from '../../services/stepper-state.service';

// Import actual components from code-window
import {
  SplitScreenComponent,
  LeftPanelComponent,
  RightPanelComponent,
  IconsComponent,
} from '@awe/play-comp-library';
import { ChatWindowComponent } from '../chat-window/chat-window.component';
import { CodeViewerComponent } from '../code-viewer/code-viewer.component';
import { MarkdownModule } from 'ngx-markdown';

// File model for code viewer
interface FileModel {
  name: string;
  language: string;
  content: string;
  path: string;
  type: 'file';
}

// Mock data from your project-loading.json file
const MOCK_PROJECT_DATA: ProjectLoadingResponse = {
  project_details: {
    project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
    project_name: "MindfulPrompts",
    project_description: "A mindfulness journal application that guides users with daily prompts to foster reflection and awareness.",
    created_at: "2025-07-14T06:17:16.085016+00:00",
    last_modified: "2025-07-14T06:17:19.777862+00:00",
    created_by: "5d0ddc8d-b1dd-4165-96e9-c1522914948e",
    project_state: "ACTIVE"
  },
  project_settings: {
    project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
    device: "web",
    framework: "react",
    design_system: "tailwind",
    generation_type: "app_generation"
  },
  repository_details: {
    project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
    vcs_provider: "azure_devops",
    clone_url: "https://<EMAIL>/ascendionava/experience-studio-preview-projects/_git/mindfulprompts-948e-1393",
    deployment_provider: "azure_static_web_apps",
    deployed_url: "https://yellow-mushroom-0bbe5cc0f.6.azurestaticapps.net"
  },
  metadata: JSON.stringify([
    {
      log: "Starting project generation...",
      status: "IN_PROGRESS",
      progress: "OVERVIEW",
      progress_description: "Analyzing project requirements and creating overview",
      metadata: [
        {
          type: "artifacts",
          data: {
            type: "text",
            data: "Creating a mindfulness journal application with daily prompts for reflection and awareness."
          }
        }
      ],
      event_type: "generation_start",
      project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
      is_final: false
    },
    {
      log: "Initializing seed project structure...",
      status: "IN_PROGRESS",
      progress: "SEED_PROJECT_INITIALIZED",
      progress_description: "Setting up React project with Tailwind CSS",
      metadata: [
        {
          type: "repository",
          data: {
            name: "mindfulprompts",
            url: "https://dev.azure.com/ascendionava/experience-studio-preview-projects/_git/mindfulprompts-948e-1393",
            clone_url: "https://<EMAIL>/ascendionava/experience-studio-preview-projects/_git/mindfulprompts-948e-1393",
            commit_sha: "initial-commit"
          }
        }
      ],
      event_type: "seed_project",
      project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
      is_final: false
    },
    {
      log: "Mapping design system components...",
      status: "IN_PROGRESS",
      progress: "DESIGN_SYSTEM_MAPPED",
      progress_description: "Configuring Tailwind CSS design tokens and components",
      metadata: [
        {
          type: "artifacts",
          data: {
            type: "json",
            data: {
              colors: {
                primary: "#6366f1",
                secondary: "#8b5cf6",
                accent: "#06b6d4"
              },
              typography: {
                fontFamily: "Inter, sans-serif",
                headings: "Poppins, sans-serif"
              }
            }
          }
        }
      ],
      event_type: "design_system",
      project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
      is_final: false
    },
    {
      log: "Generating React components...",
      status: "IN_PROGRESS",
      progress: "COMPONENTS_CREATED",
      progress_description: "Creating reusable UI components for the application",
      metadata: [
        {
          type: "files",
          data: [
            {
              fileName: "src/components/PromptCard.tsx",
              content: "import React from 'react';\n\ninterface PromptCardProps {\n  title: string;\n  content: string;\n}\n\nexport const PromptCard: React.FC<PromptCardProps> = ({ title, content }) => {\n  return (\n    <div className=\"bg-white rounded-lg shadow-md p-6\">\n      <h3 className=\"text-lg font-semibold mb-2\">{title}</h3>\n      <p className=\"text-gray-600\">{content}</p>\n    </div>\n  );\n};"
            }
          ]
        }
      ],
      event_type: "components",
      project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
      is_final: false
    },
    {
      log: "Analyzing layout structure...",
      status: "COMPLETED",
      progress: "LAYOUT_ANALYZED",
      progress_description: "Optimizing responsive layout and navigation structure",
      metadata: [
        {
          type: "artifact",
          data: {
            type: "text",
            data: "HBF"
          }
        }
      ],
      event_type: "layout",
      project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
      is_final: false
    },
    {
      log: "Generating application pages...",
      status: "IN_PROGRESS",
      progress: "PAGES_GENERATED",
      progress_description: "Creating main application pages and routing",
      metadata: [
        {
          type: "files",
          data: [
            {
              fileName: "src/pages/Dashboard.tsx",
              content: "import React from 'react';\nimport { PromptCard } from '../components/PromptCard';\n\nexport const Dashboard: React.FC = () => {\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">MindfulPrompts</h1>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <PromptCard \n          title=\"Morning Reflection\" \n          content=\"What are three things you're grateful for today?\" \n        />\n      </div>\n    </div>\n  );\n};"
            }
          ]
        }
      ],
      event_type: "pages",
      project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
      is_final: false
    },
    {
      log: "Building application...",
      status: "IN_PROGRESS",
      progress: "BUILD",
      progress_description: "Compiling and optimizing the application for deployment",
      metadata: [
        {
          type: "files",
          data: [
            {
              fileName: "src/App.tsx",
              content: "import { useEffect } from \"react\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { Dashboard } from './pages/Dashboard';\n\nfunction App() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Dashboard />\n      <Toaster />\n    </div>\n  );\n}\n\nexport default App;"
            },
            {
              fileName: "src/pages/Settings.tsx",
              content: "import React, { useState, useEffect } from 'react';\n\n// Custom Components\nimport Header from '@/components/layout/Header';\n\nexport const Settings: React.FC = () => {\n  const [theme, setTheme] = useState('light');\n\n  return (\n    <div className=\"settings-page\">\n      <Header />\n      <main className=\"container mx-auto px-4 py-8\">\n        <h1 className=\"text-2xl font-bold mb-6\">Settings</h1>\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <label className=\"block mb-4\">\n            <span className=\"text-gray-700\">Theme:</span>\n            <select \n              value={theme} \n              onChange={(e) => setTheme(e.target.value)}\n              className=\"mt-1 block w-full rounded-md border-gray-300\"\n            >\n              <option value=\"light\">Light</option>\n              <option value=\"dark\">Dark</option>\n            </select>\n          </label>\n        </div>\n      </main>\n    </div>\n  );\n};"
            }
          ]
        }
      ],
      event_type: "build",
      project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
      is_final: false
    },
    {
      log: "Deploying to Azure Static Web Apps...",
      status: "COMPLETED",
      progress: "DEPLOY",
      progress_description: "Your MindfulPrompts application has been successfully deployed!",
      metadata: [
        {
          type: "ref_code",
          data: "https://yellow-mushroom-0bbe5cc0f.6.azurestaticapps.net"
        }
      ],
      event_type: "deployment",
      project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
      is_final: true
    }
  ]),
  conversation: [
    {
      message_id: "5a98cf74-59bb-4fb6-8955-d58beac6c529",
      conversation_id: "c3a9856f-b71a-418f-ad6f-c76aed4060a5",
      message_type: "user",
      content: "change it into dark theme",
      ui_metadata: null,
      created_at: "2025-07-14T06:27:47.637332+00:00"
    },
    {
      message_id: "ddb9836f-7823-472d-9250-c07563850da1",
      conversation_id: "c3a9856f-b71a-418f-ad6f-c76aed4060a5",
      message_type: "assistant",
      content: "Great news! I've successfully regenerated the code as per your request. Let me know if there's anything else you need.",
      ui_metadata: `[{"data": [{"content": "import { useEffect } from \\"react\\";\\nimport { Toaster } from \\"@/components/ui/toaster\\";", "fileName": "src/App.tsx"}, {"content": "import React, { useState, useEffect } from 'react';\\n\\n// Custom Components\\nimport Header from '@/components/layout/Header';", "fileName": "src/pages/Settings.tsx"}], "type": "files"}]`,
      created_at: "2025-07-14T06:27:47.861614+00:00"
    },
    {
      message_id: "0f4fcbe0-56b8-4887-a5dd-f62a3dd624e7",
      conversation_id: "c3a9856f-b71a-418f-ad6f-c76aed4060a5",
      message_type: "capsule",
      content: "4efb3f991b63be1d145e64d3b89fdf2c7759f2fe",
      ui_metadata: null,
      created_at: "2025-07-14T06:27:48.064609+00:00"
    }
  ]
};

@Component({
  selector: 'app-project-loading-demo',
  standalone: true,
  imports: [
    CommonModule,
    SplitScreenComponent,
    LeftPanelComponent,
    RightPanelComponent,
    ChatWindowComponent,
    CodeViewerComponent,
    IconsComponent,
    MarkdownModule
  ],
  template: `
    <!-- Demo Header -->
    <div class="demo-header">
      <h1>🚀 Project Loading Demo - {{parsedData?.projectName || 'MindfulPrompts'}}</h1>
      <div class="loading-state">
        <span class="state-indicator" [class]="loadingState$ | async">
          {{(loadingState$ | async) | titlecase}}
        </span>
      </div>
    </div>

    <!-- Code Window Structure -->
    <awe-splitscreen class="container smooth-split-screen" [isResizable]="true" [minWidth]="'300'"
      defaultLeftPanelWidth="35%" defaultRightPanelWidth="65%">

      <!-- Left Panel - Chat Window -->
      <awe-leftpanel [hasHeader]="true" awe-leftpanel>
        <div awe-leftpanel-header>
          <div class="custom-header light-theme">
            <div class="header-left">
              <awe-icons iconName="awe_home" iconColor="neutralIcon" class="cursor-pointer"
                (click)="goBack()" title="Navigate to home page"></awe-icons>
            </div>
            <div class="header-center">
              <div class="project-name">
                {{parsedData?.projectName || 'MindfulPrompts'}}
              </div>
            </div>
            <div class="header-right">
              <span class="project-status">Loaded</span>
            </div>
          </div>
        </div>

        <div awe-leftpanel-content>
          <!-- Chat Window with Stepper Integration -->
          <div class="adjust-height">
            <app-chat-window
              #chatWindow
              [theme]="'light'"
              [defaultText]="'Project loaded successfully'"
              [chatMessages]="chatMessages"
              [showStepper]="true"
              [progress]="stepperProgress"
              [progressDescription]="stepperDescription"
              [status]="stepperStatus"
              [isCodeGenerationComplete]="true"
              [projectId]="parsedData?.projectId || ''"
              [jobId]="'demo-job-id'"
              [useApi]="false">
            </app-chat-window>
          </div>
        </div>
      </awe-leftpanel>

      <!-- Right Panel - Code Viewer and Artifacts -->
      <awe-rightpanel [hasHeader]="true" awe-rightpanel>
        <div awe-rightpanel-header>
          <div class="tabs-container">
            <div class="tab-buttons">
              <button class="tab-button"
                [class.active]="currentView === 'preview'"
                (click)="switchView('preview')">
                <awe-icons iconName="awe_preview" iconColor="neutralIcon"></awe-icons>
                Preview
              </button>
              <button class="tab-button"
                [class.active]="currentView === 'code'"
                (click)="switchView('code')">
                <awe-icons iconName="awe_code" iconColor="neutralIcon"></awe-icons>
                Code
              </button>
              <button class="tab-button"
                [class.active]="currentView === 'artifacts'"
                (click)="switchView('artifacts')">
                <awe-icons iconName="awe_artifacts" iconColor="neutralIcon"></awe-icons>
                Artifacts
              </button>
            </div>
          </div>
        </div>

        <div awe-rightpanel-content>
          <!-- Preview View -->
          <div *ngIf="currentView === 'preview'" class="preview-view">
            <div class="preview-container">
              <div class="preview-header">
                <h3>🌐 Live Preview</h3>
                <a [href]="parsedData?.deployedUrl" target="_blank" class="preview-link">
                  Open in new tab →
                </a>
              </div>
              <iframe
                [src]="parsedData?.deployedUrl"
                class="preview-iframe"
                frameborder="0">
              </iframe>
            </div>
          </div>

          <!-- Code View -->
          <div *ngIf="currentView === 'code'" class="code-view">
            <app-code-viewer
              [theme]="'light'"
              [files]="codeFiles"
              [showFileExplorer]="true">
            </app-code-viewer>
          </div>

          <!-- Artifacts View -->
          <div *ngIf="currentView === 'artifacts'" class="artifacts-view">
            <div class="artifacts-container">
              <div class="artifacts-header">
                <h3>📁 Project Artifacts</h3>
              </div>
              <div class="artifacts-content">
                <div class="artifact-section">
                  <h4>🔄 Regeneration History</h4>
                  <div *ngIf="!parsedData?.hasRegenerationData" class="no-regenerations">
                    <div class="no-regenerations-message">
                      <span class="icon">ℹ️</span>
                      <span class="text">No regenerations found for this project</span>
                    </div>
                  </div>
                  <div *ngIf="parsedData?.hasRegenerationData" class="regeneration-list">
                    <div class="regeneration-summary">
                      <span class="count">{{parsedData?.regenerationCount || 0}} regeneration(s) found</span>
                      <span class="files-count">{{parsedData?.regenerationFiles?.length || 0}} file(s) updated</span>
                    </div>
                    <div *ngFor="let message of conversationMessages; let i = index"
                         class="regeneration-item"
                         [class.has-files]="message.fileData && message.fileData.length > 0">
                      <div class="regeneration-header">
                        <span class="regeneration-type">{{message.messageType | titlecase}}</span>
                        <span class="regeneration-time">{{message.createdAt | date:'short'}}</span>
                        <span *ngIf="message.fileData && message.fileData.length > 0"
                              class="files-badge">{{message.fileData.length}} files</span>
                      </div>
                      <div class="regeneration-content">{{message.content}}</div>
                      <div *ngIf="message.fileData && message.fileData.length > 0" class="files-list">
                        <div *ngFor="let file of message.fileData" class="file-item">
                          <span class="file-name">{{file.fileName}}</span>
                          <span class="file-language">({{file.language}})</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="artifact-section">
                  <h4>🎨 Artifacts & Logs</h4>

                  <!-- Project Overview Text (if available) -->
                  <div *ngIf="getProjectOverviewText()" class="project-overview">
                    <h5>📋 Project Overview</h5>
                    <div class="overview-content">
                      <pre>{{getProjectOverviewText()}}</pre>
                    </div>
                  </div>

                  <!-- Artifacts List -->
                  <div *ngIf="!parsedData?.artifactsData || parsedData?.artifactsData?.length === 0" class="no-artifacts">
                    <div class="no-artifacts-message">
                      <span class="icon">📦</span>
                      <span class="text">No artifacts found in project metadata</span>
                    </div>
                  </div>
                  <div *ngIf="parsedData?.artifactsData && (parsedData?.artifactsData?.length || 0) > 0" class="artifacts-list">
                    <div class="artifacts-summary">
                      <span class="count">{{parsedData?.artifactsData?.length || 0}} artifact(s) found</span>
                      <span class="types">{{getArtifactTypes()}} types</span>
                    </div>
                    <div *ngFor="let artifact of parsedData?.artifactsData; let i = index"
                         class="artifact-item"
                         [class.selected]="selectedArtifact === artifact"
                         (click)="selectArtifact(artifact)">
                      <div class="artifact-header">
                        <span class="artifact-name">{{artifact.name}}</span>
                        <span class="artifact-type" [class]="'type-' + artifact.type">{{artifact.type}}</span>
                      </div>
                      <div class="artifact-source">{{artifact.source}}</div>
                      <div class="artifact-progress">Stage: {{artifact.progress}}</div>
                    </div>
                  </div>

                  <!-- Log Entries -->
                  <div *ngIf="parsedData?.logEntries && (parsedData?.logEntries?.length || 0) > 0" class="log-entries">
                    <h5>📝 SSE Log Timeline</h5>
                    <div class="log-list">
                      <div *ngFor="let logEntry of parsedData?.logEntries; let i = index"
                           class="log-item"
                           [class.completed]="logEntry.status === 'COMPLETED'">
                        <div class="log-header">
                          <span class="log-number">{{i + 1}}</span>
                          <span class="log-progress">{{logEntry.progress}}</span>
                          <span class="log-status" [class]="logEntry.status.toLowerCase()">{{logEntry.status}}</span>
                        </div>
                        <div class="log-description">{{logEntry.progress_description}}</div>
                        <div class="log-content">{{logEntry.log}}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div *ngIf="selectedArtifact" class="artifact-section">
                  <h4>🔍 Artifact Details</h4>
                  <div class="artifact-viewer">
                    <div class="artifact-viewer-header">
                      <span class="artifact-viewer-name">{{selectedArtifact.name}}</span>
                      <span class="artifact-viewer-type">{{selectedArtifact.type}}</span>
                    </div>
                    <div class="artifact-content" [ngSwitch]="selectedArtifact.type">
                      <div *ngSwitchCase="'json'" class="json-content">
                        <pre>{{formatJsonContent(selectedArtifact.content)}}</pre>
                      </div>
                      <div *ngSwitchCase="'ref_code'" class="ref-code-content">
                        <a [href]="selectedArtifact.content" target="_blank" class="ref-link">
                          {{selectedArtifact.content}}
                        </a>
                      </div>
                      <div *ngSwitchCase="'text'" class="text-content">
                        <div class="text-content-formatted">{{selectedArtifact.content}}</div>
                      </div>
                      <div *ngSwitchCase="'files'" class="files-content">
                        <div class="files-summary">{{getFilesCount(selectedArtifact.content)}} files</div>
                        <div *ngFor="let file of getFilesList(selectedArtifact.content)" class="file-item">
                          <span class="file-name">{{file.fileName}}</span>
                          <span class="file-size">({{file.content?.length || 0}} chars)</span>
                        </div>
                      </div>
                      <div *ngSwitchCase="'repository'" class="repository-content">
                        <div class="repo-info">
                          <div><strong>Name:</strong> {{selectedArtifact.content.name}}</div>
                          <div><strong>URL:</strong> <a [href]="selectedArtifact.content.url" target="_blank">{{selectedArtifact.content.url}}</a></div>
                          <div><strong>Clone URL:</strong> {{selectedArtifact.content.clone_url}}</div>
                          <div><strong>Commit:</strong> {{selectedArtifact.content.commit_sha}}</div>
                        </div>
                      </div>
                      <div *ngSwitchDefault class="default-content">
                        <pre>{{formatJsonContent(selectedArtifact.content)}}</pre>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="artifact-section">
                  <h4>📊 Version History</h4>
                  <div class="version-list">
                    <div *ngFor="let commitId of parsedData?.commitIds; let i = index"
                         class="version-item">
                      <div class="version-header">
                        <span class="version-number">v1.{{i + 1}}</span>
                        <span class="commit-id">{{commitId.substring(0, 8)}}...</span>
                      </div>
                      <div class="version-description">Code regeneration {{i + 1}}</div>
                    </div>
                  </div>
                </div>

                <div class="artifact-section">
                  <h4>🔗 Repository Details</h4>
                  <div class="repo-details">
                    <div class="repo-item">
                      <label>Clone URL:</label>
                      <code>{{parsedData?.cloneUrl}}</code>
                    </div>
                    <div class="repo-item">
                      <label>Framework:</label>
                      <span>{{parsedData?.framework | titlecase}}</span>
                    </div>
                    <div class="repo-item">
                      <label>Design System:</label>
                      <span>{{parsedData?.designSystem | titlecase}}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </awe-rightpanel>
    </awe-splitscreen>


  `,
  styleUrls: ['./project-loading-demo.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProjectLoadingDemoComponent implements OnInit {
  private readonly router = inject(Router);
  private readonly destroyRef = inject(DestroyRef);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly stepperStateService = inject(StepperStateService);

  @ViewChild('chatWindow') chatWindow!: ChatWindowComponent;

  // State management
  public readonly loadingState$ = new BehaviorSubject<ProjectLoadingState>('idle');
  public parsedData: ParsedProjectData | null = null;
  public chatMessages: any[] = [];
  public conversationMessages: any[] = [];
  public codeFiles: FileModel[] = [];
  public currentView: 'preview' | 'code' | 'artifacts' = 'preview';

  // SSE-like stepper properties (matching real SSE data structure)
  public stepperProgress: string = 'INITIALIZING';
  public stepperDescription: string = 'Preparing to load project...';
  public stepperStatus: string = 'IN_PROGRESS';

  // Artifact selection state
  public selectedArtifact: any = null;

  // SSE simulation state
  private sseEvents: Array<{
    log: string;
    status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    progress: string;
    progress_description: string;
    metadata: Array<{ type: string; data: any }>;
    event_type?: string;
    project_id?: string;
    is_final?: boolean;
  }> = [];

  ngOnInit(): void {
    // Simulate the project loading process
    this.simulateProjectLoading();
  }

  private simulateProjectLoading(): void {
    this.loadingState$.next('loading');

    // Parse the project data first
    this.parsedData = ProjectLoadingParser.parseProjectLoadingResponse(MOCK_PROJECT_DATA);

    // Parse real SSE events from metadata field
    this.parseRealSSEEvents();

    // Setup initial chat message with project description (like real SSE flow)
    this.setupInitialChatMessage();

    // Simulate SSE events with proper timing using real data
    this.simulateSSEFlow();
  }

  private parseRealSSEEvents(): void {
    if (!this.parsedData?.metadata) {
      console.warn('No metadata found for SSE events');
      return;
    }

    // Parse SSE events from the metadata field
    this.sseEvents = ProjectLoadingParser.parseSSEEventsFromMetadata(this.parsedData.metadata);

    console.log('Parsed SSE events:', this.sseEvents);
  }

  private setupInitialChatMessage(): void {
    if (!this.parsedData) return;

    // Start with project description as initial user prompt (matching real flow)
    this.chatMessages = [
      {
        text: this.parsedData.userPrompt,
        from: 'user',
        theme: 'light'
      }
    ];
  }

  private simulateSSEFlow(): void {
    if (this.sseEvents.length === 0) {
      console.warn('No SSE events to simulate');
      this.loadingState$.next('loaded');
      return;
    }

    // Reset stepper state before starting simulation
    this.stepperStateService.triggerStepperReset();

    // Start with initial stepper state
    this.stepperStatus = 'IN_PROGRESS';
    this.stepperProgress = 'INITIALIZING';
    this.stepperDescription = 'Starting project generation...';

    // Trigger change detection to show initial state
    this.cdr.markForCheck();

    console.log(`🚀 Starting SSE simulation with ${this.sseEvents.length} events`);

    // Process each SSE event with realistic timing
    this.sseEvents.forEach((event, index) => {
      const delay = (index + 1) * 1500; // 1.5s between each event for better visibility

      timer(delay).pipe(
        takeUntilDestroyed(this.destroyRef)
      ).subscribe(() => {
        this.processSSEEvent(event, index);

        // Trigger change detection after each event
        this.cdr.markForCheck();

        // Mark as loaded when final event is processed or when is_final is true
        if (index === this.sseEvents.length - 1 || event.is_final) {
          this.loadingState$.next('loaded');
        }
      });
    });
  }



  private processSSEEvent(event: any, index: number): void {
    // Update stepper properties to show current step progression
    this.stepperStatus = event.status || 'IN_PROGRESS';
    this.stepperProgress = event.progress || 'PROCESSING';
    this.stepperDescription = event.progress_description || event.log || 'Processing...';

    console.log(`🔄 SSE Event ${index + 1}/${this.sseEvents.length}:`, {
      log: event.log,
      status: event.status,
      progress: event.progress,
      description: event.progress_description,
      metadataCount: event.metadata?.length || 0,
      event_type: event.event_type
    });

    // Process metadata (files, ref_code, artifacts, etc.)
    if (event.metadata && event.metadata.length > 0) {
      event.metadata.forEach((meta: any) => {
        if (meta.type === 'files') {
          // Files are available, setup code viewer
          this.setupCodeFiles();
          console.log('📁 Code files processed for step:', event.progress);
        } else if (meta.type === 'ref_code') {
          // Deployment URL is ready
          console.log('🔗 Deployment URL ready:', meta.data);
        } else if (meta.type === 'repository') {
          // Repository information
          console.log('📦 Repository created:', meta);
        } else if (meta.type === 'artifact') {
          // Artifact data (design tokens, etc.)
          console.log('🎨 Artifact data processed:', meta);
        }
      });
    }

    // Add assistant response when generation completes (final event)
    if (event.is_final || (event.status === 'COMPLETED' && event.progress === 'DEPLOY')) {
      // Final step - mark as completed
      this.stepperStatus = 'COMPLETED';
      this.addAssistantResponse();
      console.log('✅ Project generation completed');
    }
  }

  private addAssistantResponse(): void {
    if (!this.parsedData) return;

    // Add the assistant response from conversation data
    const assistantMessage = this.parsedData.conversationMessages.find(
      msg => msg.messageType === 'assistant'
    );

    if (assistantMessage) {
      this.chatMessages.push({
        text: assistantMessage.content,
        from: 'ai',
        theme: 'light'
      });
    }
  }





  private setupCodeFiles(): void {
    if (!this.parsedData) return;

    this.codeFiles = [];

    // Check if we have regeneration data
    if (this.parsedData.hasRegenerationData && this.parsedData.regenerationFiles.length > 0) {
      console.log(`Found ${this.parsedData.regenerationCount} regenerations with ${this.parsedData.regenerationFiles.length} files`);

      // Use regeneration files (from conversation ui_metadata)
      this.parsedData.regenerationFiles.forEach(file => {
        this.codeFiles.push({
          name: file.fileName,
          language: file.language || 'typescript',
          content: file.content,
          path: file.path,
          type: 'file'
        });
      });
    } else if (this.parsedData.initialGenerationFiles.length > 0) {
      console.log(`No regenerations found, using initial generation files (${this.parsedData.initialGenerationFiles.length} files)`);

      // Use initial generation files (from metadata BUILD event)
      this.parsedData.initialGenerationFiles.forEach(file => {
        this.codeFiles.push({
          name: file.fileName,
          language: file.language || 'typescript',
          content: file.content,
          path: file.path,
          type: 'file'
        });
      });
    } else {
      console.warn('No code files found in either regeneration or initial generation data');

      // Fallback: Add demo files for demonstration
      this.codeFiles = [
        {
          name: 'src/App.tsx',
          language: 'typescript',
          content: `// No code files found in project data
// This is a demo file for visualization purposes

import React from 'react';

const App = () => {
  return (
    <div className="app">
      <h1>MindfulPrompts</h1>
      <p>A mindfulness journal application</p>
    </div>
  );
};

export default App;`,
          path: 'src/App.tsx',
          type: 'file'
        }
      ];
    }

    console.log(`Code viewer initialized with ${this.codeFiles.length} files`);
  }

  public switchView(view: 'preview' | 'code' | 'artifacts'): void {
    this.currentView = view;
  }

  public simulateLoading(): void {
    this.parsedData = null;
    this.chatMessages = [];
    this.codeFiles = [];
    this.conversationMessages = [];
    this.simulateProjectLoading();
  }

  /**
   * Get unique artifact types for display
   */
  public getArtifactTypes(): string {
    if (!this.parsedData?.artifactsData) return '0';

    const types = new Set(this.parsedData.artifactsData.map(artifact => artifact.type));
    return types.size.toString();
  }

  /**
   * Select an artifact for detailed view
   */
  public selectArtifact(artifact: any): void {
    this.selectedArtifact = artifact;
    console.log('Selected artifact:', artifact);
  }

  /**
   * Get Project Overview text from artifacts
   */
  public getProjectOverviewText(): string | null {
    if (!this.parsedData?.artifactsData) return null;

    const overviewArtifact = this.parsedData.artifactsData.find(
      artifact => artifact.type === 'text' && artifact.name === 'Project Overview'
    );

    return overviewArtifact?.content || null;
  }

  /**
   * Get files count from files artifact
   */
  public getFilesCount(filesContent: any): number {
    if (!Array.isArray(filesContent)) return 0;
    return filesContent.length;
  }

  /**
   * Get files list from files artifact
   */
  public getFilesList(filesContent: any): any[] {
    if (!Array.isArray(filesContent)) return [];
    return filesContent;
  }

  /**
   * Format JSON content for display
   */
  public formatJsonContent(content: any): string {
    try {
      if (typeof content === 'string') {
        return JSON.stringify(JSON.parse(content), null, 2);
      }
      return JSON.stringify(content, null, 2);
    } catch (error) {
      return content?.toString() || 'Invalid JSON';
    }
  }

  public goBack(): void {
    this.router.navigate(['/experience/main']);
  }
}
