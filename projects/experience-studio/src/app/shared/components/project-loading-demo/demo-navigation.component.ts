import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-demo-navigation',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="demo-navigation">
      <div class="nav-header">
        <h2>🚀 Project Loading Demo Navigation</h2>
        <p>Click the links below to see different project loading scenarios</p>
      </div>
      
      <div class="nav-cards">
        <div class="nav-card">
          <div class="card-icon">📱</div>
          <h3>MindfulPrompts Project</h3>
          <p>React + Tailwind mindfulness journal app with dark theme regeneration</p>
          <div class="card-details">
            <span class="detail-badge">React</span>
            <span class="detail-badge">Tailwind</span>
            <span class="detail-badge">Web</span>
          </div>
          <div class="card-actions">
            <a routerLink="/experience/project-loading-demo" class="btn-demo">
              View Demo
            </a>
            <a routerLink="/experience/project/fa9dbd20-6a09-45d3-bd10-e63ed4551393" class="btn-live">
              Live Route
            </a>
          </div>
        </div>

        <div class="nav-card">
          <div class="card-icon">🔄</div>
          <h3>Recent Project Flow</h3>
          <p>Simulates clicking on a recent project card from the main dashboard</p>
          <div class="card-details">
            <span class="detail-badge">Health Check</span>
            <span class="detail-badge">Project Loading</span>
            <span class="detail-badge">Code Window</span>
          </div>
          <div class="card-actions">
            <button class="btn-demo" (click)="simulateRecentProjectClick()">
              Simulate Click
            </button>
          </div>
        </div>

        <div class="nav-card">
          <div class="card-icon">📊</div>
          <h3>Data Structure</h3>
          <p>View the parsed project-loading.json data structure</p>
          <div class="card-details">
            <span class="detail-badge">JSON</span>
            <span class="detail-badge">TypeScript</span>
            <span class="detail-badge">Interfaces</span>
          </div>
          <div class="card-actions">
            <button class="btn-demo" (click)="showDataStructure()">
              View Data
            </button>
          </div>
        </div>
      </div>

      <div class="implementation-notes">
        <h3>🔧 Implementation Notes</h3>
        <ul>
          <li><strong>Route:</strong> <code>/experience/project-loading-demo</code></li>
          <li><strong>Data Source:</strong> <code>project-loading.json</code> from your file system</li>
          <li><strong>Components:</strong> Chat window, stepper, version accordion simulation</li>
          <li><strong>Features:</strong> Repository integration, conversation history, commit tracking</li>
        </ul>
      </div>
    </div>
  `,
  styles: [`
    .demo-navigation {
      padding: 2rem;
      max-width: 1200px;
      margin: 0 auto;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .nav-header {
      text-align: center;
      margin-bottom: 3rem;
      
      h2 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #495057;
      }
      
      p {
        font-size: 1.2rem;
        color: #6c757d;
      }
    }

    .nav-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .nav-card {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      border: 1px solid #e9ecef;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
      }

      .card-icon {
        font-size: 3rem;
        text-align: center;
        margin-bottom: 1rem;
      }

      h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #495057;
        text-align: center;
      }

      p {
        color: #6c757d;
        line-height: 1.5;
        margin-bottom: 1.5rem;
        text-align: center;
      }

      .card-details {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: center;
        margin-bottom: 1.5rem;

        .detail-badge {
          background: #e9ecef;
          color: #495057;
          padding: 0.25rem 0.75rem;
          border-radius: 12px;
          font-size: 0.85rem;
          font-weight: 500;
        }
      }

      .card-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
      }
    }

    .btn-demo, .btn-live {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 6px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
      display: inline-block;
      text-align: center;
      font-size: 0.9rem;
    }

    .btn-demo {
      background: #007bff;
      color: white;

      &:hover {
        background: #0056b3;
        transform: translateY(-1px);
      }
    }

    .btn-live {
      background: #28a745;
      color: white;

      &:hover {
        background: #1e7e34;
        transform: translateY(-1px);
      }
    }

    .implementation-notes {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 2rem;
      border-left: 4px solid #007bff;

      h3 {
        color: #495057;
        margin-bottom: 1rem;
      }

      ul {
        list-style: none;
        padding: 0;

        li {
          margin-bottom: 0.75rem;
          color: #6c757d;
          line-height: 1.5;

          code {
            background: #e9ecef;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.85rem;
          }
        }
      }
    }

    @media (max-width: 768px) {
      .demo-navigation {
        padding: 1rem;
      }

      .nav-header h2 {
        font-size: 2rem;
      }

      .nav-cards {
        grid-template-columns: 1fr;
      }

      .card-actions {
        flex-direction: column;
      }
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DemoNavigationComponent {
  
  simulateRecentProjectClick(): void {
    // This would simulate the recent project card click flow
    console.log('Simulating recent project card click...');
    
    // In a real implementation, this would:
    // 1. Show loading overlay on the card
    // 2. Perform health check
    // 3. Navigate to project loading route
    // 4. Load project data and initialize code-window
    
    alert('This would simulate clicking a recent project card and navigating to the project loading view!');
  }

  showDataStructure(): void {
    // This would show the data structure
    console.log('Showing data structure...');
    
    const dataStructure = {
      project_details: {
        project_id: "fa9dbd20-6a09-45d3-bd10-e63ed4551393",
        project_name: "MindfulPrompts",
        project_description: "A mindfulness journal application...",
        // ... other fields
      },
      project_settings: {
        device: "web",
        framework: "react",
        design_system: "tailwind",
        generation_type: "app_generation"
      },
      repository_details: {
        clone_url: "https://<EMAIL>/...",
        deployed_url: "https://yellow-mushroom-0bbe5cc0f.6.azurestaticapps.net"
      },
      conversation: [
        // Array of conversation messages
      ]
    };
    
    console.log('Project Loading Data Structure:', dataStructure);
    alert('Check the browser console to see the data structure!');
  }
}
