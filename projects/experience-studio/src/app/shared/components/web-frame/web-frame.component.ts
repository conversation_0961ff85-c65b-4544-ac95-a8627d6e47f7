import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, inject, OnChanges, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SafeSrcdocDirective } from '../../directives/safe-srcdoc.directive';
import { createLogger } from '../../utils/logger';
import { ScreenshotService } from '../../services/screenshot.service';

export interface WebPage {
  fileName: string;
  content: string;
}

@Component({
  selector: 'app-web-frame',
  standalone: true,
  imports: [CommonModule, SafeSrcdocDirective],
  templateUrl: './web-frame.component.html',
  styleUrls: ['./web-frame.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WebFrameComponent implements OnChanges {
  private readonly logger = createLogger('WebFrameComponent');
  private readonly screenshotService = inject(ScreenshotService);

  @ViewChild('webIframe') webIframe!: ElementRef<HTMLIFrameElement>;

  @Input() pages: WebPage[] = [];
  @Input() currentPageIndex: number = 0;
  @Input() theme: 'light' | 'dark' = 'light';

  @Output() pageChange = new EventEmitter<number>();
  @Output() fullscreenRequest = new EventEmitter<WebPage>();

  ngOnChanges(changes: SimpleChanges): void {
    // Handle changes to inputs if needed
    if (changes['currentPageIndex'] || changes['pages']) {
      this.logger.debug('Web frame inputs changed', {
        currentPageIndex: this.currentPageIndex,
        pagesCount: this.pages.length
      });
    }
  }

  get currentPage(): WebPage | null {
    return this.pages[this.currentPageIndex] || null;
  }

  get currentPageContent(): string {
    const content = this.currentPage?.content || '';
    if (!content) return '';

    // Enhance content with basic CSS for better web display
    return this.enhanceContentForWeb(content);
  }

  private enhanceContentForWeb(content: string): string {
    // Basic CSS for web frame display
    const basicCSS = `
      <style>
        body {
          margin: 0;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
          background: #fff;
          min-height: 100vh;
          box-sizing: border-box;
        }
        * {
          box-sizing: border-box;
        }
        h1, h2, h3, h4, h5, h6 {
          margin-top: 0;
          margin-bottom: 1rem;
        }
        p {
          margin-bottom: 1rem;
        }
        img {
          max-width: 100%;
          height: auto;
        }
        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
        }
      </style>
    `;

    // If content is just a fragment, wrap it in a basic HTML structure
    if (!content.includes('<body>')) {
      return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  ${basicCSS}
</head>
<body>
  <div class="container">
    ${content}
  </div>
</body>
</html>`;
    }

    // If content has body but no head styling, inject CSS into head or body
    if (content.includes('<head>')) {
      return content.replace('<head>', `<head>${basicCSS}`);
    } else if (content.includes('<body>')) {
      return content.replace('<body>', `<body>${basicCSS}`);
    }

    return content;
  }

  onPageSelect(index: number): void {
    if (index >= 0 && index < this.pages.length && index !== this.currentPageIndex) {
      this.pageChange.emit(index);
    }
  }

  onPreviousPage(): void {
    if (this.currentPageIndex > 0) {
      this.pageChange.emit(this.currentPageIndex - 1);
    }
  }

  onNextPage(): void {
    if (this.currentPageIndex < this.pages.length - 1) {
      this.pageChange.emit(this.currentPageIndex + 1);
    }
  }

  onFullscreenClick(): void {
    if (this.currentPage) {
      this.fullscreenRequest.emit(this.currentPage);
    }
  }

  onOpenInNewTabClick(): void {
    this.logger.info('🔗 Web frame open in new tab button clicked', {
      hasCurrentPage: !!this.currentPage,
      currentPageFileName: this.currentPage?.fileName
    });

    if (!this.currentPage?.content) {
      this.logger.warn('🔗 Cannot open in new tab - no current page or content');
      return;
    }

    this.logger.info('🔗 Opening web wireframe page in new tab', {
      pageTitle: this.getPageDisplayName(this.currentPage),
      hasContent: !!this.currentPage.content
    });

    try {
      // Create a new window/tab
      const newWindow = window.open('', '_blank');

      if (newWindow) {
        // Write the HTML content directly to the new window (exactly like modal implementation)
        // No processing - use raw content just like the modal does
        newWindow.document.write(this.currentPage.content);
        newWindow.document.close();

        // Set the page title
        const title = this.getPageDisplayName(this.currentPage) || 'Wireframe Preview';
        newWindow.document.title = title;

        this.logger.info('🔗 Web wireframe page opened in new tab successfully');
      } else {
        // Fallback: use blob URL if popup is blocked
        this.openWithBlobUrl(this.currentPage.content);
      }
    } catch (error) {
      this.logger.error('🔗 Failed to open in new tab, using blob URL fallback', error);
      // Fallback: use blob URL
      this.openWithBlobUrl(this.currentPage.content);
    }
  }

  private openWithBlobUrl(content: string): void {
    // Use raw content without processing (exactly like modal implementation)
    const blob = new Blob([content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    window.open(url, '_blank');

    // Clean up the blob URL after a short delay
    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 1000);
  }

  onDownloadClick(): void {
    this.logger.info('📸 Web frame download button clicked', {
      hasCurrentPage: !!this.currentPage,
      hasWebIframe: !!this.webIframe,
      currentPageFileName: this.currentPage?.fileName
    });

    if (!this.currentPage) {
      this.logger.warn('📸 Cannot download screenshot - no current page');
      return;
    }

    if (!this.webIframe) {
      this.logger.warn('📸 Cannot download screenshot - no iframe reference');
      return;
    }

    const iframe = this.webIframe.nativeElement;

    this.logger.info('📸 Starting web frame screenshot capture', {
      iframeExists: !!iframe,
      iframeSrc: iframe.src,
      iframeLoaded: iframe.contentDocument !== null,
      iframeWidth: iframe.offsetWidth,
      iframeHeight: iframe.offsetHeight
    });

    // Use viewport-aware screenshot with web mode
    this.screenshotService.captureViewportAwareScreenshot(
      'web',
      undefined,
      iframe,
      this.currentPage.fileName
    ).subscribe({
      next: () => {
        this.logger.info('📸 Web frame screenshot downloaded successfully');
      },
      error: (error) => {
        this.logger.error('📸 Web frame screenshot failed', error);
      }
    });
  }

  getPageDisplayName(page: WebPage): string {
    // Remove .html extension and format the name
    return page.fileName.replace('.html', '').replace(/_/g, ' ');
  }

  getPageUrl(): string {
    // Generate a mock URL for the address bar
    const pageName = this.currentPage?.fileName.replace('.html', '') || 'home';
    return `/${pageName}`;
  }
}
