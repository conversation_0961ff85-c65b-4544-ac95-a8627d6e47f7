// Web Frame Component Styles
.web-frame-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  width: 100%;
  height: 80vh;
  overflow: hidden;
  background: var(--code-viewer-bg);
  border: 1px solid var(--code-viewer-border);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  // Theme variables
  &.light-theme {
    --placeholder-bg: #f9fafb;
    --placeholder-text: #6b7280;
    --browser-bg: #ffffff;
    --browser-border: #e5e7eb;
    --header-bg: #f3f4f6;
    --control-bg: #ef4444;
    --control-minimize: #f59e0b;
    --control-maximize: #10b981;
    --address-bg: #ffffff;
    --address-border: #d1d5db;
    --nav-btn-bg: #f9fafb;
    --nav-btn-hover: #f3f4f6;
  }

  &.dark-theme {
    --placeholder-bg: #1f2937;
    --placeholder-text: #9ca3af;
    --browser-bg: #1f2937;
    --browser-border: #374151;
    --header-bg: #111827;
    --control-bg: #ef4444;
    --control-minimize: #f59e0b;
    --control-maximize: #10b981;
    --address-bg: #374151;
    --address-border: #4b5563;
    --nav-btn-bg: #374151;
    --nav-btn-hover: #4b5563;
  }
}

// Device Frame Container
.device-frame-container {
  width: 100%;
  height: 80vh;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

// Browser Window
.browser-window {
  width: 100%;
  height: 100%;
  background: var(--browser-bg);
  border: 1px solid var(--browser-border);
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

// Browser Header
.browser-header {
  background: var(--header-bg);
  border-bottom: 1px solid var(--browser-border);
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 48px;
}

// Window Controls (Mac style)
.window-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.control-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  cursor: pointer;

  &.close {
    background: var(--control-bg);
  }

  &.minimize {
    background: var(--control-minimize);
  }

  &.maximize {
    background: var(--control-maximize);
  }
}

// Navigation Controls
.nav-controls {
  display: flex;
  gap: 4px;
  align-items: center;
}

.nav-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--address-border);
  border-radius: 6px;
  background: var(--nav-btn-bg);
  color: var(--placeholder-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--nav-btn-hover);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// Address Bar
.address-bar {
  flex: 1;
  margin: 0 12px;
}

.url-container {
  background: var(--address-bg);
  border: 1px solid var(--address-border);
  border-radius: 20px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}


.url-text {
  color: var(--placeholder-text);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Action Buttons
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-button {
  width: 36px;
  height: 36px;
  border: 1px solid var(--address-border);
  border-radius: 6px;
  background: var(--nav-btn-bg);
  color: var(--placeholder-text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--nav-btn-hover);
    color: #3b82f6;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// Browser Content
.browser-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.web-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: #ffffff;
}

// No Content Placeholder
.no-content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: var(--placeholder-bg);
  color: var(--placeholder-text);
  text-align: center;
  padding: 40px;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.placeholder-description {
  font-size: 14px;
  opacity: 0.8;
  max-width: 300px;
  line-height: 1.5;
}

// Responsive Design
@media (max-width: 768px) {
  .browser-header {
    padding: 6px 12px;
    gap: 8px;
  }

  .address-bar {
    margin: 0 8px;
  }

  .url-container {
    padding: 6px 12px;
    font-size: 12px;
  }

  .action-button {
    width: 32px;
    height: 32px;
  }
}
