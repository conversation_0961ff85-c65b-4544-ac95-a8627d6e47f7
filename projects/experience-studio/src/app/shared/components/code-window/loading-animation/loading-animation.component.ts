import { Component, Input, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject } from 'rxjs';
import { TextTransformationService } from '../../../services/text-transformation.service';

@Component({
  selector: 'app-loading-animation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './loading-animation.component.html',
  styleUrls: ['./loading-animation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LoadingAnimationComponent implements OnInit, OnDestroy {
  @Input() messages: string[] = [];
  @Input() theme: 'light' | 'dark' = 'dark';

  currentMessage$ = new BehaviorSubject<string>('');

  private currentIndex = 0;
  private messageInterval: any;
  private transformedMessages: string[] = [];

  constructor(private textTransformationService: TextTransformationService) {}

  ngOnInit() {
    this.transformMessages();
    this.rotateMessages();
  }

  ngOnDestroy() {
    if (this.messageInterval) {
      clearInterval(this.messageInterval);
    }
  }

  private transformMessages() {
    this.transformedMessages = this.textTransformationService.transformMessages(this.messages);

    if (this.transformedMessages.length === 0) {
      this.transformedMessages = ['Loading...'];
    }
  }

  private rotateMessages() {
    this.currentMessage$.next(this.transformedMessages[0]);

    if (this.transformedMessages.length > 1) {
      this.messageInterval = setInterval(() => {
        this.currentIndex = (this.currentIndex + 1) % this.transformedMessages.length;
        this.currentMessage$.next(this.transformedMessages[this.currentIndex]);
      }, 3000);
    }
  }
}
