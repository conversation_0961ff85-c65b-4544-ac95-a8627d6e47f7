import { Injectable, signal, computed, inject, DestroyRef, NgZone } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable } from 'rxjs';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

export interface FileModel {
  name: string;
  type: 'file' | 'folder';
  content?: string;
  fileName?: string;
  language?: string;
  path?: string;
}

export interface FileData {
  path: string;
  code: string;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowFileManagerService {
  private destroyRef = inject(DestroyRef);
  private ngZone = inject(NgZone);
  private sanitizer = inject(DomSanitizer);

  readonly currentFiles = signal<FileModel[]>([]);
  readonly isCodeGenerationComplete = signal<boolean>(false);
  readonly isViewingSeedProjectTemplate = signal<boolean>(false);
  readonly selectedFileName = signal<string>('');
  readonly fileCount = signal<number>(0);

  private files$ = new BehaviorSubject<FileModel[]>([]);
  private codeFileName$ = new BehaviorSubject<string>('ui-design.html');
  private highlightedCode$ = new BehaviorSubject<string>('');
  private codeLineNumbers$ = new BehaviorSubject<number[]>([]);

  private lastProcessedContent = '';
  private codeLines: string[] = [];

  readonly files = this.files$.asObservable();
  readonly codeFileName = this.codeFileName$.asObservable();
  readonly highlightedCode = this.highlightedCode$.asObservable();
  readonly codeLineNumbers = this.codeLineNumbers$.asObservable();

  readonly hasFiles = computed(() => this.currentFiles().length > 0);
  readonly canViewCode = computed(() =>
    this.hasFiles() && this.isCodeGenerationComplete()
  );
  readonly fileTypesSummary = computed(() =>
    this.getFileTypesSummary(this.currentFiles())
  );

  constructor() {
    this.initializeFileManager();
  }

  updateCodeViewer(files: FileData[]): void {
    if (files && files.length > 0) {
      this.ngZone.run(() => {
        const fileModels: FileModel[] = files.map(file => ({
          name: file.path,
          type: 'file' as const,
          content: file.code,
          fileName: file.path,
        }));

        this.setFiles(fileModels);
        this.isCodeGenerationComplete.set(true);
      });
    }
  }

  handleCodeUpdate(generatedCode: any): void {
    try {
      let fileModels: FileModel[] = [];

      if (Array.isArray(generatedCode)) {
        fileModels = generatedCode.map(file => ({
          name: file.fileName || file.name || 'unknown.txt',
          type: 'file' as const,
          content: file.content || '',
          fileName: file.fileName || file.name || 'unknown.txt',
        }));
      } else if (typeof generatedCode === 'object' && generatedCode !== null) {
        fileModels = Object.entries(generatedCode).map(([path, content]) => ({
          name: path,
          type: 'file' as const,
          content: typeof content === 'string' ? content : JSON.stringify(content, null, 2),
          fileName: path,
        }));
      }

      if (fileModels.length > 0) {
        this.setFiles(fileModels);
        this.isCodeGenerationComplete.set(true);
      }
    } catch (error) {
      console.error('Error handling code update:', error);
    }
  }

  processCodeFiles(files: FileData[]): void {
    if (files && files.length > 0) {
      this.ngZone.run(() => {
        const fileModels = files.map(file => {
          const fullPath = file.path;
          const language = this.getLanguageFromPath(file.path);

          return {
            name: fullPath,
            language: language,
            content: file.code,
            path: fullPath,
            type: 'file' as const,
          };
        });

        this.setFiles(fileModels);
        this.fileCount.set(fileModels.length);

        if (fileModels.length > 0) {
          this.isViewingSeedProjectTemplate.set(false);
          this.isCodeGenerationComplete.set(true);
        }
      });
    }
  }

  sortFilesByTechnology(files: FileModel[], technology: string): FileModel[] {
    if (!files || files.length === 0) {
      return files;
    }

    const filePriority: { [key: string]: { [key: string]: number } } = {
      react: {
        'src/components/': 100,
        'components/': 100,
        'src/pages/': 99,
        'pages/': 99,
        'src/services/': 98,
        'services/': 98,
        'src/utils/': 97,
        'utils/': 97,
        'src/hooks/': 96,
        'hooks/': 96,
        'src/': 95,
        'public/': 90,
        'package.json': 85,
        'README.md': 80,
        '.': 70
      },
      angular: {
        'src/app/': 100,
        'src/app/components/': 99,
        'src/app/services/': 98,
        'src/app/pages/': 97,
        'src/': 95,
        'angular.json': 90,
        'package.json': 85,
        'README.md': 80,
        '.': 70
      },
      vue: {
        'src/components/': 100,
        'src/views/': 99,
        'src/': 95,
        'public/': 90,
        'package.json': 85,
        'README.md': 80,
        '.': 70
      }
    };

    const priorities = filePriority[technology.toLowerCase()] || filePriority['react'];

    return files.sort((a, b) => {
      const pathA = a.name || a.fileName || '';
      const pathB = b.name || b.fileName || '';

      const priorityA = this.getFilePriority(pathA, priorities);
      const priorityB = this.getFilePriority(pathB, priorities);

      if (priorityA !== priorityB) {
        return priorityB - priorityA;
      }

      return pathA.localeCompare(pathB);
    });
  }

  getLanguageFromPath(path: string): string {
    const extension = path.split('.').pop()?.toLowerCase();

    const languageMap: { [key: string]: string } = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'html': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'java': 'java',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml'
    };

    return languageMap[extension || ''] || 'plaintext';
  }

  getFileTypesSummary(files: FileModel[]): Record<string, number> {
    const summary: Record<string, number> = {};

    files.forEach(file => {
      const fileName = file.fileName || file.name || '';
      let extension = fileName.split('.').pop()?.toLowerCase() || 'no-extension';

      const extensionGroups: Record<string, string> = {
        'js': 'javascript',
        'jsx': 'javascript',
        'ts': 'typescript',
        'tsx': 'typescript',
        'css': 'styles',
        'scss': 'styles',
        'sass': 'styles',
        'less': 'styles',
        'html': 'markup',
        'htm': 'markup',
        'xml': 'markup',
        'json': 'data',
        'yaml': 'data',
        'yml': 'data',
        'md': 'documentation',
        'txt': 'documentation'
      };

      const group = extensionGroups[extension] || extension;
      summary[group] = (summary[group] || 0) + 1;
    });

    return summary;
  }

  updateCodeData(rawContent: string, title?: string): void {
    if (!rawContent) {
      this.codeLineNumbers$.next([]);
      this.highlightedCode$.next('');
      this.codeFileName$.next('ui-design.html');
      return;
    }

    if (this.lastProcessedContent !== rawContent) {
      this.codeLines = rawContent.split('\n');
      const lineNumbers = Array.from({ length: this.codeLines.length }, (_, i) => i + 1);
      const highlightedCode = this.generateHighlightedCode(rawContent);

      const fileName = title
        ? `${title.toLowerCase().replace(/[^a-z0-9]/g, '-')}.html`
        : 'ui-design.html';

      this.codeLineNumbers$.next(lineNumbers);
      this.highlightedCode$.next(highlightedCode);
      this.codeFileName$.next(fileName);

      this.lastProcessedContent = rawContent;
    }
  }

  generateHighlightedCode(code: string): string {
    code = code
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;');

    code = code
      .replace(/(&lt;\/?)([a-zA-Z][a-zA-Z0-9]*)(.*?)(&gt;)/g,
        '<span class="html-tag">$1</span><span class="html-tag-name">$2</span><span class="html-attributes">$3</span><span class="html-tag">$4</span>')
      .replace(/(\w+)(=)(&quot;[^&]*&quot;)/g,
        '<span class="html-attr-name">$1</span><span class="html-operator">$2</span><span class="html-attr-value">$3</span>')
      .replace(/(&lt;!--.*?--&gt;)/g, '<span class="html-comment">$1</span>')
      .replace(/(&lt;!DOCTYPE.*?&gt;)/gi, '<span class="html-doctype">$1</span>');

    return code;
  }

  setFiles(files: FileModel[]): void {
    this.currentFiles.set(files);
    this.files$.next(files);
    this.fileCount.set(files.length);
  }

  clearFiles(): void {
    this.setFiles([]);
    this.isCodeGenerationComplete.set(false);
    this.isViewingSeedProjectTemplate.set(false);
    this.selectedFileName.set('');
  }

  private getFilePriority(path: string, priorities: { [key: string]: number }): number {
    for (const [prefix, priority] of Object.entries(priorities)) {
      if (path.startsWith(prefix)) {
        return priority;
      }
    }
    return 0;
  }

  private initializeFileManager(): void {
    this.destroyRef.onDestroy(() => {
      this.clearFiles();
    });
  }

  getCurrentFileState(): {
    signals: {
      fileCount: number;
      hasFiles: boolean;
      canViewCode: boolean;
      isCodeGenerationComplete: boolean;
      isViewingSeedProjectTemplate: boolean;
    };
    files: FileModel[];
    summary: Record<string, number>;
  } {
    return {
      signals: {
        fileCount: this.fileCount(),
        hasFiles: this.hasFiles(),
        canViewCode: this.canViewCode(),
        isCodeGenerationComplete: this.isCodeGenerationComplete(),
        isViewingSeedProjectTemplate: this.isViewingSeedProjectTemplate(),
      },
      files: this.currentFiles(),
      summary: this.fileTypesSummary()
    };
  }
}
