import { Injectable, NgZone, ChangeDetectorRef } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { CodeWindowCoreStateService } from './code-window-core-state.service';
import { CodeWindowPreviewStateService } from './code-window-preview-state.service';
import { CodeWindowUIDesignStateService } from './code-window-ui-design-state.service';

export type TabType = 'preview' | 'code' | 'artifacts' | 'history' | 'overview';

export interface TabSwitchOptions {
  automatic?: boolean;
  reason?: string;
  skipTransition?: boolean;
}

export interface TabSwitchEvent {
  fromTab: TabType;
  toTab: TabType;
  reason: string;
  automatic: boolean;
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowTabManagementService {
  private userSelectedTab = false;
  private tabSwitchHistory: TabSwitchEvent[] = [];
  
  // Events
  private tabSwitched$ = new Subject<TabSwitchEvent>();
  
  // Public observables
  readonly tabSwitched = this.tabSwitched$.asObservable();
  
  constructor(
    private coreStateService: CodeWindowCoreStateService,
    private previewStateService: CodeWindowPreviewStateService,
    private uiDesignStateService: CodeWindowUIDesignStateService
  ) {}
  
  // Handle automatic code tab switch
  handleAutomaticCodeTabSwitch(ngZone: NgZone, cdr: ChangeDetectorRef): void {
    if (this.userSelectedTab) {
      return;
    }

    // Check if tab transition is in progress
    const isTransitioning = this.coreStateService.getCurrentTabState().isTransitioning;
    if (isTransitioning) {
      return;
    }

    // Check if UI design mode is active
    const isUIDesignMode = this.uiDesignStateService.getUIDesignMode();
    if (isUIDesignMode) {
      return;
    }

    this.switchTab('code', 'Automatic code generation completion', { automatic: true }, ngZone, cdr);
  }
  
  // Handle automatic preview tab switch
  handleAutomaticPreviewTabSwitch(ngZone: NgZone, cdr: ChangeDetectorRef): void {
    if (this.userSelectedTab) {
      return;
    }

    // Check if tab transition is in progress
    const isTransitioning = this.coreStateService.getCurrentTabState().isTransitioning;
    if (isTransitioning) {
      return;
    }

    // Check if UI design mode is active
    const isUIDesignMode = this.uiDesignStateService.getUIDesignMode();
    if (isUIDesignMode) {
      return;
    }

    this.switchTab('preview', 'Automatic preview ready', { automatic: true }, ngZone, cdr);
  }
  
  // Handle automatic error tab switch
  handleAutomaticErrorTabSwitch(ngZone: NgZone, cdr: ChangeDetectorRef): void {
    // Get current error description from service
    this.previewStateService.errorDescription.subscribe(errorDesc => {
      this.coreStateService.setCurrentTabState({
        activeTab: 'preview',
        isTransitioning: true,
        lastError: errorDesc
      });
    }).unsubscribe();

    this.switchTab('preview', 'Error occurred', { automatic: true }, ngZone, cdr);
  }
  
  // Handle automatic failed tab switch
  handleAutomaticFailedTabSwitch(ngZone: NgZone, cdr: ChangeDetectorRef): void {
    this.switchTab('preview', 'Generation failed', { automatic: true }, ngZone, cdr);
  }
  
  // Unified tab switching method
  switchTab(
    targetTab: TabType, 
    reason: string, 
    options: TabSwitchOptions = {}, 
    ngZone: NgZone, 
    cdr: ChangeDetectorRef
  ): void {
    const currentTabState = this.coreStateService.getCurrentTabState();
    const fromTab = this.getCurrentActiveTab();
    
    // Record tab switch event
    const switchEvent: TabSwitchEvent = {
      fromTab,
      toTab: targetTab,
      reason,
      automatic: options.automatic || false,
      timestamp: Date.now()
    };
    
    // Add to history
    this.tabSwitchHistory.push(switchEvent);
    
    // Keep only last 50 switches
    if (this.tabSwitchHistory.length > 50) {
      this.tabSwitchHistory = this.tabSwitchHistory.slice(-50);
    }
    
    if (!options.skipTransition) {
      this.coreStateService.setTabTransitionInProgress(true);
      this.coreStateService.setCurrentTabState({
        activeTab: targetTab,
        isTransitioning: true,
        lastError: targetTab === 'preview' && reason.includes('error') ? reason : null
      });
    }

    ngZone.run(() => {
      // Activate the target tab
      this.coreStateService.activateTab(targetTab);

      if (!options.skipTransition) {
        setTimeout(() => {
          this.coreStateService.setTabTransitionInProgress(false);
          this.coreStateService.updateTabState({
            isTransitioning: false
          });
          cdr.detectChanges();
        }, 200);
      }
    });
    
    // Emit tab switch event
    this.tabSwitched$.next(switchEvent);
  }
  
  // Get current active tab
  getCurrentActiveTab(): TabType {
    const currentView = this.coreStateService.getCurrentView();
    
    switch (currentView) {
      case 'editor':
        return 'code';
      case 'preview':
        return 'preview';
      case 'artifacts':
        return 'artifacts';
      case 'overview':
        return 'overview';
      default:
        return 'preview';
    }
  }
  
  // Set user selected tab flag
  setUserSelectedTab(selected: boolean): void {
    this.userSelectedTab = selected;
  }
  
  // Check if user has manually selected a tab
  hasUserSelectedTab(): boolean {
    return this.userSelectedTab;
  }
  
  // Get tab switch history
  getTabSwitchHistory(): TabSwitchEvent[] {
    return [...this.tabSwitchHistory];
  }
  
  // Clear tab switch history
  clearTabSwitchHistory(): void {
    this.tabSwitchHistory = [];
  }
  
  // Check if tab is available
  isTabAvailable(tab: TabType): boolean {
    switch (tab) {
      case 'preview':
        return this.previewStateService.getCurrentPreviewTabState().isEnabled;
      case 'code':
        return this.previewStateService.getCurrentCodeTabState().isEnabled;
      case 'artifacts':
        // Check if artifacts are available
        return true; // Simplified for now
      case 'overview':
        return this.uiDesignStateService.getUIDesignMode();
      case 'history':
        return true;
      default:
        return false;
    }
  }
  
  // Get tab tooltip message
  getTabTooltip(tab: TabType): string {
    switch (tab) {
      case 'preview':
        return this.previewStateService.getCurrentPreviewTabState().tooltipMessage;
      case 'code':
        return this.previewStateService.getCurrentCodeTabState().tooltipMessage;
      case 'artifacts':
        return 'View generated artifacts and design tokens';
      case 'overview':
        return 'View UI design overview';
      case 'history':
        return 'View conversation history';
      default:
        return '';
    }
  }
  
  // Reset tab management state
  reset(): void {
    this.userSelectedTab = false;
    this.tabSwitchHistory = [];
  }
  
  // Cleanup method for component destruction
  cleanup(): void {
    this.tabSwitched$.complete();
  }
}
