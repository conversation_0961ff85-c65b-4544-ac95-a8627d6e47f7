import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';

export interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  editable: boolean;
}

export interface DesignTokenEditState {
  isEditMode: boolean;
  editButtonText: 'Edit' | 'Save' | 'Done';
  hasUnsavedChanges: boolean;
  originalValues: Map<string, string>;
}

export interface DesignTokensState {
  tokens: DesignToken[];
  isLoading: boolean;
  editState: DesignTokenEditState;
  designSystemData: any;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowDesignTokensManagementService {
  private designTokensState$ = new BehaviorSubject<DesignTokensState>({
    tokens: [],
    isLoading: false,
    editState: {
      isEditMode: false,
      editButtonText: 'Edit',
      hasUnsavedChanges: false,
      originalValues: new Map()
    },
    designSystemData: null
  });

  // Events
  private tokensInitialized$ = new Subject<{ tokens: DesignToken[]; source: string }>();
  private tokenUpdated$ = new Subject<{ tokenId: string; oldValue: string; newValue: string }>();
  private editModeChanged$ = new Subject<{ isEditMode: boolean; hasChanges: boolean }>();

  // Public observables
  readonly designTokensState = this.designTokensState$.asObservable();
  readonly tokensInitialized = this.tokensInitialized$.asObservable();
  readonly tokenUpdated = this.tokenUpdated$.asObservable();
  readonly editModeChanged = this.editModeChanged$.asObservable();

  constructor() {}

  // Initialize design tokens
  initializeDesignTokens(
    designSystemData: any,
    allDesignTokens: DesignToken[],
    callbacks: {
      startDesignTokenLoading: () => void;
      stopDesignTokenLoading: () => void;
    }
  ): void {
    const currentState = this.designTokensState$.value;

    if (designSystemData && this.hasNewDesignTokenStructure(designSystemData)) {
      this.initializeFromNewStructure(designSystemData, callbacks);
      return;
    }

    if (!designSystemData || this.isUsingDefaultTokens(currentState.tokens)) {
      callbacks.startDesignTokenLoading();
      this.updateTokens([], 'default');
      return;
    }

    this.updateTokens([...allDesignTokens], 'existing');
  }

  // Check if data has new design token structure
  hasNewDesignTokenStructure(data: any): boolean {
    try {
      return data?.colors && Array.isArray(data.colors) &&
        data.colors.length > 0 &&
        data.colors[0]?.id && data.colors[0]?.name && data.colors[0]?.value;
    } catch (error) {
      return false;
    }
  }

  // Initialize from new structure
  private initializeFromNewStructure(
    data: any,
    callbacks: {
      stopDesignTokenLoading: () => void;
      startDesignTokenLoading: () => void;
    }
  ): void {
    try {
      const newTokens: DesignToken[] = [];

      if (data.colors && Array.isArray(data.colors)) {
        data.colors.forEach((colorToken: any) => {
          if (this.isValidArtifactDesignToken(colorToken)) {
            const mappedToken: DesignToken = {
              id: colorToken.id,
              name: colorToken.name,
              value: colorToken.value,
              type: 'color',
              category: colorToken.category || 'Colors',
              editable: colorToken.editable !== false
            };
            newTokens.push(mappedToken);
          }
        });
      }

      this.updateTokens(newTokens, 'new-structure');
      callbacks.stopDesignTokenLoading();

    } catch (error) {
      callbacks.startDesignTokenLoading();
      this.updateTokens([], 'error');
    }
  }

  // Validate artifact design token
  private isValidArtifactDesignToken(token: any): boolean {
    return token &&
      typeof token.id === 'string' && token.id.trim() !== '' &&
      typeof token.name === 'string' && token.name.trim() !== '' &&
      typeof token.value === 'string' && token.value.trim() !== '' &&
      (typeof token.category === 'string' || token.category === undefined) &&
      (typeof token.editable === 'boolean' || token.editable === undefined);
  }

  // Check if using default tokens
  isUsingDefaultTokens(tokens: DesignToken[]): boolean {
    if (tokens.length === 0) return true;

    const colorTokens = tokens.filter(token => token.category === 'Colors');
    const defaultColorNames = ['D_Yellow', 'Lemon', 'Black', 'D_Grey', 'Silver'];

    return colorTokens.length === 5 &&
      colorTokens.every(token => defaultColorNames.includes(token.name));
  }

  // Generate color name from hex
  generateColorName(hexColor: string): string {
    const hex = hexColor.startsWith('#') ? hexColor.substring(1) : hexColor;

    const colorMap: { [key: string]: string } = {
      '000000': 'Black',
      FFFFFF: 'White',
      FF0000: 'Red',
      '00FF00': 'Green',
      '0000FF': 'Blue',
      FFFF00: 'Yellow',
      '00FFFF': 'Cyan',
      FF00FF: 'Magenta',
      C0C0C0: 'Silver',
      '808080': 'Gray',
      '800000': 'Maroon',
      '808000': 'Olive',
      '008000': 'Dark Green',
      '800080': 'Purple',
      '008080': 'Teal',
      '000080': 'Navy',
      FFA500: 'Orange',
      A52A2A: 'Brown',
      FFC0CB: 'Pink',
      E48900: 'D_Yellow',
      FFA826: 'Lemon',
      '212121': 'Black',
      '4D4D4D': 'D_Grey',
      F5F7FA: 'Silver',
    };

    if (colorMap[hex.toUpperCase()]) {
      return colorMap[hex.toUpperCase()];
    }

    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    let hue = '';
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);

    if (max === min) {
      const brightness = Math.round((r + g + b) / 3);
      if (brightness < 64) return 'Dark Gray';
      if (brightness < 128) return 'Gray';
      if (brightness < 192) return 'Light Gray';
      return 'Off White';
    }

    if (r === max) {
      if (g > b) hue = 'Orange';
      else hue = 'Red';
    } else if (g === max) {
      if (r > b) hue = 'Yellow Green';
      else hue = 'Green';
    } else {
      if (r > g) hue = 'Purple';
      else hue = 'Blue';
    }

    const brightness = (r + g + b) / 3;
    let prefix = '';

    if (brightness < 85) prefix = 'Dark ';
    else if (brightness > 170) prefix = 'Light ';

    return prefix + hue;
  }

  // Update design token
  updateDesignToken(tokenId: string, newValue: string): void {
    const currentState = this.designTokensState$.value;

    if (!currentState.editState.isEditMode) {
      return;
    }

    const tokenIndex = currentState.tokens.findIndex(token => token.id === tokenId);

    if (tokenIndex !== -1) {
      const token = { ...currentState.tokens[tokenIndex] };
      const originalValue = currentState.editState.originalValues.get(tokenId);

      const oldValue = token.value;
      token.value = newValue;

      if (token.type === 'color') {
        token.name = this.generateColorName(newValue);
      }

      const hasChanged = originalValue !== newValue;
      const updatedTokens = [...currentState.tokens];
      updatedTokens[tokenIndex] = token;

      const newEditState = {
        ...currentState.editState,
        hasUnsavedChanges: hasChanged || currentState.editState.hasUnsavedChanges
      };

      this.designTokensState$.next({
        ...currentState,
        tokens: updatedTokens,
        editState: newEditState
      });

      this.tokenUpdated$.next({ tokenId, oldValue, newValue });
    }
  }

  // Get design tokens for backend
  getDesignTokensForBackend(): any {
    const tokens = this.designTokensState$.value.tokens;

    return {
      colors: tokens
        .filter(token => token.type === 'color')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          hexCode: token.value,
          category: token.category,
          editable: token.editable,
        })),
      typography: tokens
        .filter(token => token.type === 'typography')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          category: token.category,
          editable: token.editable,
        })),
      buttons: tokens
        .filter(token => token.category === 'Buttons')
        .map(token => ({
          id: token.id,
          name: token.name,
          value: token.value,
          category: token.category,
          editable: token.editable,
        }))
    };
  }

  // Update tokens
  private updateTokens(tokens: DesignToken[], source: string): void {
    const currentState = this.designTokensState$.value;

    this.designTokensState$.next({
      ...currentState,
      tokens
    });

    this.tokensInitialized$.next({ tokens, source });
  }

  // Enter edit mode
  enterEditMode(): void {
    const currentState = this.designTokensState$.value;
    const originalValues = new Map<string, string>();

    currentState.tokens.forEach(token => {
      if (token.category === 'Colors') {
        originalValues.set(token.id, token.value);
      }
    });

    const newEditState: DesignTokenEditState = {
      isEditMode: true,
      editButtonText: 'Save',
      hasUnsavedChanges: false,
      originalValues
    };

    this.designTokensState$.next({
      ...currentState,
      editState: newEditState
    });

    this.editModeChanged$.next({ isEditMode: true, hasChanges: false });
  }

  // Save changes
  saveChanges(): void {
    const currentState = this.designTokensState$.value;

    const newEditState: DesignTokenEditState = {
      isEditMode: false,
      editButtonText: 'Edit',
      hasUnsavedChanges: false,
      originalValues: new Map()
    };

    this.designTokensState$.next({
      ...currentState,
      editState: newEditState
    });

    this.editModeChanged$.next({ isEditMode: false, hasChanges: false });
  }

  // Get current tokens
  getCurrentTokens(): DesignToken[] {
    return this.designTokensState$.value.tokens;
  }

  // Get edit state
  getEditState(): DesignTokenEditState {
    return this.designTokensState$.value.editState;
  }

  // Set loading state
  setLoading(isLoading: boolean): void {
    const currentState = this.designTokensState$.value;
    this.designTokensState$.next({
      ...currentState,
      isLoading
    });
  }

  // Reset service state
  reset(): void {
    this.designTokensState$.next({
      tokens: [],
      isLoading: false,
      editState: {
        isEditMode: false,
        editButtonText: 'Edit',
        hasUnsavedChanges: false,
        originalValues: new Map()
      },
      designSystemData: null
    });
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.tokensInitialized$.complete();
    this.tokenUpdated$.complete();
    this.editModeChanged$.complete();
  }
}
