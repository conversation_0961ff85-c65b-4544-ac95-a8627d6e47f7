import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface PreviewTabState {
  isVisible: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  loadingMessage?: string;
  tooltipMessage: string;
}

export interface CodeTabState {
  isVisible: boolean;
  isEnabled: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  loadingMessage?: string;
  tooltipMessage: string;
}

export enum PreviewTabStatus {
  HIDDEN = 'hidden',
  DISABLED = 'disabled',
  LOADING = 'loading',
  ENABLED = 'enabled',
  ERROR = 'error'
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowPreviewStateService {
  // Preview state
  private isPreviewLoading$ = new BehaviorSubject<boolean>(false);
  private previewIcon$ = new BehaviorSubject<string>('bi-code-slash');
  private previewError$ = new BehaviorSubject<boolean>(false);
  private errorDescription$ = new BehaviorSubject<string>('Please try again later.');
  private errorTerminalOutput$ = new BehaviorSubject<string>('');
  private deployedUrl$ = new BehaviorSubject<string | null>('');
  
  // URL validation state
  private isIframeReady$ = new BehaviorSubject<boolean>(false);
  private isUrlValidated$ = new BehaviorSubject<boolean>(false);
  private urlValidationError$ = new BehaviorSubject<string>('');
  private isUrlAvailable$ = new BehaviorSubject<boolean>(false);

  // Tab state
  private previewTabName$ = new BehaviorSubject<string>('Preview');
  private previewTabState$ = new BehaviorSubject<PreviewTabState>({
    isVisible: true,
    isEnabled: false,
    isLoading: false,
    hasError: false,
    tooltipMessage: 'Preview will be available once the application is deployed'
  });
  private previewTabStatus$ = new BehaviorSubject<PreviewTabStatus>(PreviewTabStatus.DISABLED);
  
  private codeTabState$ = new BehaviorSubject<CodeTabState>({
    isVisible: true,
    isEnabled: false,
    isLoading: false,
    hasError: false,
    tooltipMessage: 'Code will be available once generated'
  });

  // Regeneration state
  private isRegenerationInProgress$ = new BehaviorSubject<boolean>(false);
  private isCodeGenerationLoading$ = new BehaviorSubject<boolean>(false);

  // Public observables
  readonly isPreviewLoading = this.isPreviewLoading$.asObservable();
  readonly previewIcon = this.previewIcon$.asObservable();
  readonly previewError = this.previewError$.asObservable();
  readonly errorDescription = this.errorDescription$.asObservable();
  readonly errorTerminalOutput = this.errorTerminalOutput$.asObservable();
  readonly deployedUrl = this.deployedUrl$.asObservable();
  readonly isIframeReady = this.isIframeReady$.asObservable();
  readonly isUrlValidated = this.isUrlValidated$.asObservable();
  readonly urlValidationError = this.urlValidationError$.asObservable();
  readonly isUrlAvailable = this.isUrlAvailable$.asObservable();
  readonly previewTabName = this.previewTabName$.asObservable();
  readonly previewTabState = this.previewTabState$.asObservable();
  readonly previewTabStatus = this.previewTabStatus$.asObservable();
  readonly codeTabState = this.codeTabState$.asObservable();
  readonly isRegenerationInProgress = this.isRegenerationInProgress$.asObservable();
  readonly isCodeGenerationLoading = this.isCodeGenerationLoading$.asObservable();

  // State update methods
  setPreviewLoading(loading: boolean): void {
    this.isPreviewLoading$.next(loading);
  }

  setPreviewIcon(icon: string): void {
    this.previewIcon$.next(icon);
  }

  setPreviewError(error: boolean, description?: string): void {
    this.previewError$.next(error);
    if (description) {
      this.errorDescription$.next(description);
    }
  }

  setErrorDescription(description: string): void {
    this.errorDescription$.next(description);
  }

  setErrorTerminalOutput(output: string): void {
    this.errorTerminalOutput$.next(output);
  }

  setDeployedUrl(url: string | null): void {
    this.deployedUrl$.next(url);
  }

  getDeployedUrl(): string | null {
    return this.deployedUrl$.value;
  }

  setIframeReady(ready: boolean): void {
    this.isIframeReady$.next(ready);
  }

  setUrlValidated(validated: boolean): void {
    this.isUrlValidated$.next(validated);
  }

  setUrlValidationError(error: string): void {
    this.urlValidationError$.next(error);
  }

  setUrlAvailable(available: boolean): void {
    this.isUrlAvailable$.next(available);
  }

  setPreviewTabName(name: string): void {
    this.previewTabName$.next(name);
  }

  // Tab state methods
  setPreviewTabEnabled(tooltipMessage: string): void {
    this.previewTabState$.next({
      isVisible: true,
      isEnabled: true,
      isLoading: false,
      hasError: false,
      tooltipMessage
    });
    this.previewTabStatus$.next(PreviewTabStatus.ENABLED);
  }

  setPreviewTabDisabled(tooltipMessage: string): void {
    this.previewTabState$.next({
      isVisible: true,
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage
    });
    this.previewTabStatus$.next(PreviewTabStatus.DISABLED);
  }

  setPreviewTabLoading(loadingMessage: string): void {
    this.previewTabState$.next({
      isVisible: true,
      isEnabled: false,
      isLoading: true,
      hasError: false,
      loadingMessage,
      tooltipMessage: loadingMessage
    });
    this.previewTabStatus$.next(PreviewTabStatus.LOADING);
  }

  setPreviewTabError(errorMessage: string): void {
    this.previewTabState$.next({
      isVisible: true,
      isEnabled: false,
      isLoading: false,
      hasError: true,
      errorMessage,
      tooltipMessage: errorMessage
    });
    this.previewTabStatus$.next(PreviewTabStatus.ERROR);
  }

  setCodeTabEnabled(tooltipMessage: string): void {
    this.codeTabState$.next({
      isVisible: true,
      isEnabled: true,
      isLoading: false,
      hasError: false,
      tooltipMessage
    });
  }

  setCodeTabDisabled(tooltipMessage: string): void {
    this.codeTabState$.next({
      isVisible: true,
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage
    });
  }

  getCurrentPreviewTabState(): PreviewTabState {
    return this.previewTabState$.value;
  }

  getCurrentCodeTabState(): CodeTabState {
    return this.codeTabState$.value;
  }

  // Regeneration methods
  setRegenerationInProgress(inProgress: boolean): void {
    this.isRegenerationInProgress$.next(inProgress);
  }

  setCodeGenerationLoading(loading: boolean): void {
    this.isCodeGenerationLoading$.next(loading);
  }

  // Reset methods
  reset(): void {
    this.isPreviewLoading$.next(false);
    this.previewIcon$.next('bi-code-slash');
    this.previewError$.next(false);
    this.errorDescription$.next('Please try again later.');
    this.errorTerminalOutput$.next('');
    this.deployedUrl$.next('');
    this.isIframeReady$.next(false);
    this.isUrlValidated$.next(false);
    this.urlValidationError$.next('');
    this.isUrlAvailable$.next(false);
    this.previewTabName$.next('Preview');
    this.previewTabState$.next({
      isVisible: true,
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage: 'Preview will be available once the application is deployed'
    });
    this.previewTabStatus$.next(PreviewTabStatus.DISABLED);
    this.codeTabState$.next({
      isVisible: true,
      isEnabled: false,
      isLoading: false,
      hasError: false,
      tooltipMessage: 'Code will be available once generated'
    });
    this.isRegenerationInProgress$.next(false);
    this.isCodeGenerationLoading$.next(false);
  }

  resetPreviewState(): void {
    this.deployedUrl$.next(null);
    this.isPreviewLoading$.next(true);
    this.previewError$.next(false);
    this.errorDescription$.next('Please try again later.');
    this.errorTerminalOutput$.next('');
    this.previewIcon$.next('bi-code-slash');
    this.isIframeReady$.next(false);
    this.isUrlValidated$.next(false);
    this.isUrlAvailable$.next(false);
    this.urlValidationError$.next('');
  }

  // Additional getter methods
  getPreviewIcon(): string {
    return this.previewIcon$.value;
  }

  getPreviewError(): boolean {
    return this.previewError$.value;
  }

  getErrorDescription(): string {
    return this.errorDescription$.value;
  }

  getErrorTerminalOutput(): string {
    return this.errorTerminalOutput$.value;
  }
}
