import { Injectable, NgZone, ChangeDetectorRef } from '@angular/core';
import { BehaviorSubject, Observable, Subject, Subscription, interval } from 'rxjs';
import { take, takeUntil, filter } from 'rxjs/operators';
import { CodeWindowCoreStateService } from './code-window-core-state.service';
import { CodeWindowPreviewStateService } from './code-window-preview-state.service';
import { CodeWindowUIDesignStateService } from './code-window-ui-design-state.service';

export interface PollingState {
  isActive: boolean;
  projectId: string | null;
  jobId: string | null;
  intervalMs: number;
  startTime: number;
  lastResponseTime: number;
  responseCount: number;
  hasError: boolean;
  errorMessage: string | null;
}

export interface PollingOptions {
  projectId: string;
  jobId: string;
  intervalMs?: number;
  maxDuration?: number;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowPollingManagementService {
  private pollingState$ = new BehaviorSubject<PollingState>({
    isActive: false,
    projectId: null,
    jobId: null,
    intervalMs: 2000,
    startTime: 0,
    lastResponseTime: 0,
    responseCount: 0,
    hasError: false,
    errorMessage: null
  });

  private pollingSubscription: Subscription | null = null;
  private destroy$ = new Subject<void>();

  // Events
  private pollingStarted$ = new Subject<PollingOptions>();
  private pollingCompleted$ = new Subject<{ success: boolean; responseCount: number }>();
  private pollingFailed$ = new Subject<{ error: any; options: PollingOptions }>();
  private responseReceived$ = new Subject<{ data: any; responseCount: number }>();

  // Public observables
  readonly pollingState = this.pollingState$.asObservable();
  readonly pollingStarted = this.pollingStarted$.asObservable();
  readonly pollingCompleted = this.pollingCompleted$.asObservable();
  readonly pollingFailed = this.pollingFailed$.asObservable();
  readonly responseReceived = this.responseReceived$.asObservable();

  constructor(
    private coreStateService: CodeWindowCoreStateService,
    private previewStateService: CodeWindowPreviewStateService,
    private uiDesignStateService: CodeWindowUIDesignStateService
  ) {}

  // Start polling
  startPolling(options: PollingOptions): void {
    const currentState = this.pollingState$.value;
    
    if (currentState.isActive) {
      this.stopPolling();
    }

    // Update state
    this.pollingState$.next({
      isActive: true,
      projectId: options.projectId,
      jobId: options.jobId,
      intervalMs: options.intervalMs || 2000,
      startTime: Date.now(),
      lastResponseTime: 0,
      responseCount: 0,
      hasError: false,
      errorMessage: null
    });

    // Start polling interval
    this.pollingSubscription = interval(options.intervalMs || 2000)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.performPollingRequest(options);
      });

    // Emit started event
    this.pollingStarted$.next(options);
  }

  // Stop polling
  stopPolling(): void {
    const currentState = this.pollingState$.value;
    
    if (!currentState.isActive) {
      return;
    }

    // Clean up subscription
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = null;
    }

    // Update state
    this.pollingState$.next({
      ...currentState,
      isActive: false
    });

    // Emit completion event
    this.pollingCompleted$.next({
      success: true,
      responseCount: currentState.responseCount
    });
  }

  // Perform polling request
  private performPollingRequest(options: PollingOptions): void {
    const currentState = this.pollingState$.value;
    
    if (!currentState.isActive) {
      return;
    }

    // Check if UI Design mode is active - skip polling if so
    const isUIDesignMode = this.uiDesignStateService.getUIDesignMode();
    if (isUIDesignMode) {
      return;
    }

    // Make API request (this would be injected service in real implementation)
    this.makePollingAPIRequest(options)
      .then(response => {
        this.handlePollingResponse(response);
      })
      .catch(error => {
        this.handlePollingError(error, options);
      });
  }

  // Handle polling response
  private handlePollingResponse(response: any): void {
    const currentState = this.pollingState$.value;
    
    // Update state
    this.pollingState$.next({
      ...currentState,
      lastResponseTime: Date.now(),
      responseCount: currentState.responseCount + 1,
      hasError: false,
      errorMessage: null
    });

    // Process response based on type
    if (response.files && response.files.length > 0) {
      this.processFileResponse(response.files);
    }

    if (response.previewUrl) {
      this.processPreviewUrlResponse(response.previewUrl);
    }

    if (response.artifacts) {
      this.processArtifactsResponse(response.artifacts);
    }

    // Emit response event
    this.responseReceived$.next({
      data: response,
      responseCount: currentState.responseCount + 1
    });

    // Check if polling should stop based on response
    if (this.shouldStopPolling(response)) {
      this.stopPolling();
    }
  }

  // Handle polling error
  private handlePollingError(error: any, options: PollingOptions): void {
    const currentState = this.pollingState$.value;
    
    const errorMessage = error?.message || 'Polling request failed';
    
    // Update state
    this.pollingState$.next({
      ...currentState,
      hasError: true,
      errorMessage
    });

    // Emit failure event
    this.pollingFailed$.next({ error, options });

    // Continue polling on error (don't stop automatically)
  }

  // Process file response
  private processFileResponse(files: any[]): void {
    // This would delegate to FileOperationsService
  }

  // Process preview URL response
  private processPreviewUrlResponse(url: string): void {
    if (url && url.trim() !== '' && url !== 'ERROR_DEPLOYMENT_FAILED') {
      this.previewStateService.setDeployedUrl(url);
      this.previewStateService.setPreviewLoading(false);
      this.previewStateService.setPreviewError(false);
    }
  }

  // Process artifacts response
  private processArtifactsResponse(artifacts: any): void {
    // This would delegate to ArtifactsStateService
  }

  // Check if polling should stop
  private shouldStopPolling(response: any): boolean {
    // Stop if we have complete response with files and preview URL
    return response.files && 
           response.files.length > 0 && 
           response.previewUrl && 
           response.previewUrl !== 'ERROR_DEPLOYMENT_FAILED';
  }

  // Mock API request (would be replaced with real HTTP service)
  private async makePollingAPIRequest(options: PollingOptions): Promise<any> {
    // This is a mock - in real implementation would use HttpClient
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Mock response
        resolve({
          files: [],
          previewUrl: null,
          artifacts: null,
          status: 'in_progress'
        });
      }, 100);
    });
  }

  // Check if polling is active
  isPollingActive(): boolean {
    return this.pollingState$.value.isActive;
  }

  // Get current polling state
  getCurrentState(): PollingState {
    return this.pollingState$.value;
  }

  // Get response count
  getResponseCount(): number {
    return this.pollingState$.value.responseCount;
  }

  // Check if there's an error
  hasError(): boolean {
    return this.pollingState$.value.hasError;
  }

  // Get error message
  getErrorMessage(): string | null {
    return this.pollingState$.value.errorMessage;
  }

  // Reset polling state
  reset(): void {
    this.stopPolling();
    
    this.pollingState$.next({
      isActive: false,
      projectId: null,
      jobId: null,
      intervalMs: 2000,
      startTime: 0,
      lastResponseTime: 0,
      responseCount: 0,
      hasError: false,
      errorMessage: null
    });
  }

  // Subscribe to new polling processor
  subscribeToNewPollingProcessor(
    newPollingResponseProcessor: any,
    subscription: any,
    callbacks: {
      shouldBlockUIDesignData: (type: string) => boolean;
      handleNewProgressChange: (progress: any) => void;
      updatePromptBarEnabledState: () => void;
      updatePreviewTabStateForProgress: (progress: any) => void;
      updateCodeTabStateForProgress: (progress: any) => void;
      handleNewStatusChange: (status: any) => void;
      updateStepperDescription: (description: string) => void;
      updateLogWindow: (content: any) => void;
      updateArtifactsWindow: (data: any) => void;
      updateCodeViewer: (files: any) => void;
      updatePreviewWindow: (url: string) => void;
      handleAutomaticPreviewTabSwitch: () => void;
      showDeploymentErrorInPreview: () => void;
      updateProjectInfo: (projectInfo: any) => void;
    }
  ): void {
    // Progress subscription
    subscription.add(
      newPollingResponseProcessor.currentProgress$.subscribe((progress: any) => {
        if (callbacks.shouldBlockUIDesignData('progress')) {
          return;
        }
        callbacks.handleNewProgressChange(progress);
        callbacks.updatePromptBarEnabledState();
        callbacks.updatePreviewTabStateForProgress(progress);
        callbacks.updateCodeTabStateForProgress(progress);
      })
    );

    // Status subscription
    subscription.add(
      newPollingResponseProcessor.currentStatus$.subscribe((status: any) => {
        if (callbacks.shouldBlockUIDesignData('status')) {
          return;
        }
        callbacks.handleNewStatusChange(status);
        callbacks.updatePromptBarEnabledState();
        callbacks.updatePreviewTabStateForProgress(status);
        callbacks.updateCodeTabStateForProgress(status);
      })
    );

    // Progress description subscription
    subscription.add(
      newPollingResponseProcessor.progressDescription$.subscribe((description: string) => {
        if (this.uiDesignStateService.getUIDesignMode()) {
          return;
        }
        callbacks.updateStepperDescription(description);
      })
    );

    // Log content subscription
    subscription.add(
      newPollingResponseProcessor.logContent$.subscribe((content: any) => {
        if (callbacks.shouldBlockUIDesignData('logs')) {
          return;
        }
        callbacks.updateLogWindow(content);
      })
    );

    // Artifact data subscription
    subscription.add(
      newPollingResponseProcessor.artifactData$.subscribe((data: any) => {
        if (callbacks.shouldBlockUIDesignData('artifacts')) {
          return;
        }
        callbacks.updateArtifactsWindow(data);
      })
    );

    // File list subscription
    subscription.add(
      newPollingResponseProcessor.fileList$.subscribe((files: any) => {
        if (this.uiDesignStateService.getUIDesignMode()) {
          return;
        }
        // Handle file list update
      })
    );

    // Code files subscription
    subscription.add(
      newPollingResponseProcessor.codeFiles$.subscribe((files: any) => {
        if (this.uiDesignStateService.getUIDesignMode()) {
          return;
        }
        callbacks.updateCodeViewer(files);
      })
    );

    // Preview URL subscription
    subscription.add(
      newPollingResponseProcessor.previewUrl$.subscribe((url: string) => {
        if (this.uiDesignStateService.getUIDesignMode()) {
          return;
        }

        callbacks.updatePreviewWindow(url);

        if (url && url.trim() !== '' && url !== 'ERROR_DEPLOYMENT_FAILED') {
          callbacks.updatePreviewWindow(url);
          callbacks.handleAutomaticPreviewTabSwitch();
        } else if (url === 'ERROR_DEPLOYMENT_FAILED') {
          callbacks.showDeploymentErrorInPreview();
        }
      })
    );

    // Project info subscription
    subscription.add(
      newPollingResponseProcessor.projectInfo$.subscribe((projectInfo: any) => {
        if (this.uiDesignStateService.getUIDesignMode()) {
          return;
        }
        callbacks.updateProjectInfo(projectInfo);
      })
    );
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.stopPolling();
    this.destroy$.next();
    this.destroy$.complete();

    this.pollingStarted$.complete();
    this.pollingCompleted$.complete();
    this.pollingFailed$.complete();
    this.responseReceived$.complete();
  }
}
