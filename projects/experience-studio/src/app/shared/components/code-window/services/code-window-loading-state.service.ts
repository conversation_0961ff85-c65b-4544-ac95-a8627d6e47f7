import { Injectable, signal, computed, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CodeWindowLoadingStateService {
  private destroyRef = inject(DestroyRef);

  readonly isGeneralLoading = signal<boolean>(true);
  readonly isUIDesignLoading = signal<boolean>(false);
  readonly isCodeGenerationLoading = signal<boolean>(false);
  readonly isRegenerationLoading = signal<boolean>(false);
  readonly isPreviewLoading = signal<boolean>(false);
  readonly isArtifactsLoading = signal<boolean>(false);
  readonly isLayoutLoading = signal<boolean>(false);

  private isLoading$ = new BehaviorSubject<boolean>(true);
  private isPreviewLoading$ = new BehaviorSubject<boolean>(false);
  private isLayoutAnalysisLoading$ = new BehaviorSubject<boolean>(false);
  private isCodeViewLoading$ = new BehaviorSubject<boolean>(false);
  private isArtifactsViewLoading$ = new BehaviorSubject<boolean>(false);

  private isRegenerationPreviewLoading$ = new BehaviorSubject<boolean>(false);
  private isRegenerationCodeLoading$ = new BehaviorSubject<boolean>(false);
  private isRegenerationArtifactsLoading$ = new BehaviorSubject<boolean>(false);

  private isInitializationLoading$ = new BehaviorSubject<boolean>(true);
  private isBuildLoading$ = new BehaviorSubject<boolean>(false);
  private isDeployLoading$ = new BehaviorSubject<boolean>(false);

  readonly isLoading = this.isLoading$.asObservable();
  readonly isPreviewLoadingObs = this.isPreviewLoading$.asObservable();
  readonly isLayoutAnalysisLoading = this.isLayoutAnalysisLoading$.asObservable();
  readonly isCodeViewLoading = this.isCodeViewLoading$.asObservable();
  readonly isArtifactsViewLoading = this.isArtifactsViewLoading$.asObservable();

  readonly isRegenerationPreviewLoading = this.isRegenerationPreviewLoading$.asObservable();
  readonly isRegenerationCodeLoading = this.isRegenerationCodeLoading$.asObservable();
  readonly isRegenerationArtifactsLoading = this.isRegenerationArtifactsLoading$.asObservable();

  readonly isInitializationLoading = this.isInitializationLoading$.asObservable();
  readonly isBuildLoading = this.isBuildLoading$.asObservable();
  readonly isDeployLoading = this.isDeployLoading$.asObservable();

  readonly isAnyLoading = computed(() =>
    this.isGeneralLoading() ||
    this.isUIDesignLoading() ||
    this.isCodeGenerationLoading() ||
    this.isRegenerationLoading() ||
    this.isPreviewLoading() ||
    this.isArtifactsLoading() ||
    this.isLayoutLoading()
  );

  readonly shouldShowLoadingIndicator = computed(() =>
    this.isAnyLoading() && !this.isInitializationComplete()
  );

  readonly isInitializationComplete = computed(() =>
    !this.isInitializationLoading$.value
  );

  readonly combinedLoadingState = combineLatest([
    this.isLoading,
    this.isPreviewLoadingObs,
    this.isLayoutAnalysisLoading,
    this.isRegenerationPreviewLoading
  ]).pipe(
    map(([general, preview, layout, regeneration]) => ({
      general,
      preview,
      layout,
      regeneration,
      any: general || preview || layout || regeneration
    }))
  );

  constructor() {
    this.initializeLoadingStates();
  }

  shouldShowUIDesignLoadingIndicator(): boolean {
    return this.isUIDesignLoading() || this.isLayoutLoading();
  }

  shouldShowCodeGenerationLoadingIndicator(): boolean {
    return this.isCodeGenerationLoading() || this.isBuildLoading$.value;
  }

  startRegenerationPreviewLoading(): void {
    this.isRegenerationLoading.set(true);
    this.isRegenerationPreviewLoading$.next(true);
    this.isPreviewLoading.set(true);
  }

  stopRegenerationPreviewLoading(): void {
    this.isRegenerationLoading.set(false);
    this.isRegenerationPreviewLoading$.next(false);
    this.isPreviewLoading.set(false);
  }

  startUIDesignLoading(): void {
    this.isUIDesignLoading.set(true);
    this.isLayoutAnalysisLoading$.next(true);
  }

  stopUIDesignLoading(): void {
    this.isUIDesignLoading.set(false);
    this.isLayoutAnalysisLoading$.next(false);
  }

  startCodeGenerationLoading(): void {
    this.isCodeGenerationLoading.set(true);
    this.isBuildLoading$.next(true);
    this.isCodeViewLoading$.next(true);
  }

  stopCodeGenerationLoading(): void {
    this.isCodeGenerationLoading.set(false);
    this.isBuildLoading$.next(false);
    this.isCodeViewLoading$.next(false);
  }

  startGeneralLoading(): void {
    this.isGeneralLoading.set(true);
    this.isLoading$.next(true);
  }

  stopGeneralLoading(): void {
    this.isGeneralLoading.set(false);
    this.isLoading$.next(false);
  }

  startPreviewLoading(): void {
    this.isPreviewLoading.set(true);
    this.isPreviewLoading$.next(true);
  }

  stopPreviewLoading(): void {
    this.isPreviewLoading.set(false);
    this.isPreviewLoading$.next(false);
  }

  startArtifactsLoading(): void {
    this.isArtifactsLoading.set(true);
    this.isArtifactsViewLoading$.next(true);
  }

  stopArtifactsLoading(): void {
    this.isArtifactsLoading.set(false);
    this.isArtifactsViewLoading$.next(false);
  }

  startLayoutLoading(): void {
    this.isLayoutLoading.set(true);
    this.isLayoutAnalysisLoading$.next(true);
  }

  stopLayoutLoading(): void {
    this.isLayoutLoading.set(false);
    this.isLayoutAnalysisLoading$.next(false);
  }

  startInitializationLoading(): void {
    this.isInitializationLoading$.next(true);
    this.startGeneralLoading();
  }

  completeInitialization(): void {
    this.isInitializationLoading$.next(false);
    this.stopGeneralLoading();
  }

  startBuildLoading(): void {
    this.isBuildLoading$.next(true);
    this.startCodeGenerationLoading();
  }

  stopBuildLoading(): void {
    this.isBuildLoading$.next(false);
    this.stopCodeGenerationLoading();
  }

  startDeployLoading(): void {
    this.isDeployLoading$.next(true);
    this.startPreviewLoading();
  }

  stopDeployLoading(): void {
    this.isDeployLoading$.next(false);
    this.stopPreviewLoading();
  }

  resetAllLoadingStates(): void {
    this.isGeneralLoading.set(false);
    this.isUIDesignLoading.set(false);
    this.isCodeGenerationLoading.set(false);
    this.isRegenerationLoading.set(false);
    this.isPreviewLoading.set(false);
    this.isArtifactsLoading.set(false);
    this.isLayoutLoading.set(false);

    this.isLoading$.next(false);
    this.isPreviewLoading$.next(false);
    this.isLayoutAnalysisLoading$.next(false);
    this.isCodeViewLoading$.next(false);
    this.isArtifactsViewLoading$.next(false);
    this.isRegenerationPreviewLoading$.next(false);
    this.isRegenerationCodeLoading$.next(false);
    this.isRegenerationArtifactsLoading$.next(false);
    this.isInitializationLoading$.next(false);
    this.isBuildLoading$.next(false);
    this.isDeployLoading$.next(false);
  }

  getCurrentLoadingState(): {
    signals: {
      general: boolean;
      uiDesign: boolean;
      codeGeneration: boolean;
      regeneration: boolean;
      preview: boolean;
      artifacts: boolean;
      layout: boolean;
    };
    computed: {
      anyLoading: boolean;
      shouldShowIndicator: boolean;
      initializationComplete: boolean;
    };
    behaviorSubjects: {
      general: boolean;
      preview: boolean;
      layoutAnalysis: boolean;
      codeView: boolean;
      artifactsView: boolean;
    };
  } {
    return {
      signals: {
        general: this.isGeneralLoading(),
        uiDesign: this.isUIDesignLoading(),
        codeGeneration: this.isCodeGenerationLoading(),
        regeneration: this.isRegenerationLoading(),
        preview: this.isPreviewLoading(),
        artifacts: this.isArtifactsLoading(),
        layout: this.isLayoutLoading(),
      },
      computed: {
        anyLoading: this.isAnyLoading(),
        shouldShowIndicator: this.shouldShowLoadingIndicator(),
        initializationComplete: this.isInitializationComplete(),
      },
      behaviorSubjects: {
        general: this.isLoading$.value,
        preview: this.isPreviewLoading$.value,
        layoutAnalysis: this.isLayoutAnalysisLoading$.value,
        codeView: this.isCodeViewLoading$.value,
        artifactsView: this.isArtifactsViewLoading$.value,
      }
    };
  }

  private initializeLoadingStates(): void {
    this.startInitializationLoading();
  }
}
