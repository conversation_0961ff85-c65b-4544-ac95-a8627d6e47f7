import { Injectable, signal, computed, inject, DestroyRef, NgZone } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CodeWindowTabStateService {
  private destroyRef = inject(DestroyRef);
  private ngZone = inject(NgZone);

  readonly currentTab = signal<string>('preview');
  readonly isTransitioning = signal<boolean>(false);
  readonly userSelectedTab = signal<boolean>(false);

  private currentView$ = new BehaviorSubject<'loading' | 'editor' | 'preview' | 'overview' | 'logs' | 'artifacts'>('preview');
  private isCodeActive$ = new BehaviorSubject<boolean>(false);
  private isPreviewActive$ = new BehaviorSubject<boolean>(true);
  private isArtifactsActive$ = new BehaviorSubject<boolean>(false);
  private isLogsActive$ = new BehaviorSubject<boolean>(false);
  private isOverviewActive$ = new BehaviorSubject<boolean>(false);

  private isCodeViewEnhanced$ = new BehaviorSubject<boolean>(false);
  private isPreviewViewEnhanced$ = new BehaviorSubject<boolean>(true);
  private isArtifactsViewEnhanced$ = new BehaviorSubject<boolean>(false);

  private isCodeTabTransitioning$ = new BehaviorSubject<boolean>(false);
  private isPreviewTabTransitioning$ = new BehaviorSubject<boolean>(false);
  private isArtifactsTabTransitioning$ = new BehaviorSubject<boolean>(false);

  readonly currentView = this.currentView$.asObservable();
  readonly isCodeActive = this.isCodeActive$.asObservable();
  readonly isPreviewActive = this.isPreviewActive$.asObservable();
  readonly isArtifactsActive = this.isArtifactsActive$.asObservable();
  readonly isLogsActive = this.isLogsActive$.asObservable();
  readonly isOverviewActive = this.isOverviewActive$.asObservable();

  readonly isCodeViewEnhanced = this.isCodeViewEnhanced$.asObservable();
  readonly isPreviewViewEnhanced = this.isPreviewViewEnhanced$.asObservable();
  readonly isArtifactsViewEnhanced = this.isArtifactsViewEnhanced$.asObservable();

  readonly isCodeTabTransitioning = this.isCodeTabTransitioning$.asObservable();
  readonly isPreviewTabTransitioning = this.isPreviewTabTransitioning$.asObservable();
  readonly isArtifactsTabTransitioning = this.isArtifactsTabTransitioning$.asObservable();

  readonly canSwitchTabs = computed(() => !this.isTransitioning());
  readonly isAnyTabTransitioning = computed(() =>
    this.isCodeTabTransitioning$.value ||
    this.isPreviewTabTransitioning$.value ||
    this.isArtifactsTabTransitioning$.value
  );

  constructor() {
    this.resetToDefaultState();
  }

  switchTab(tab: string): void {
    if (!this.canSwitchTabs()) {
      return;
    }

    this.startTabTransition(tab);
    this.userSelectedTab.set(true);

    this.ngZone.run(() => {
      switch (tab.toLowerCase()) {
        case 'code':
        case 'editor':
          this.activateCodeTab();
          break;
        case 'preview':
          this.activatePreviewTab();
          break;
        case 'artifacts':
          this.activateArtifactsTab();
          break;
        case 'logs':
          this.activateLogsTab();
          break;
        case 'overview':
          this.activateOverviewTab();
          break;
        default:
          this.activatePreviewTab();
      }
      this.completeTabTransition();
    });
  }

  toggleCodeView(): void {
    if (this.isCodeActive$.value) {
      this.activatePreviewTab();
    } else {
      this.activateCodeTab();
    }
  }

  togglePreviewView(): void {
    if (this.isPreviewActive$.value) {
      this.activateCodeTab();
    } else {
      this.activatePreviewTab();
    }
  }

  toggleCodeViewEnhanced(): void {
    const isCurrentlyActive = this.isCodeViewEnhanced$.value;
    this.isCodeViewEnhanced$.next(!isCurrentlyActive);

    if (!isCurrentlyActive) {
      this.isPreviewViewEnhanced$.next(false);
      this.isArtifactsViewEnhanced$.next(false);
      this.currentTab.set('code');
    }
  }

  togglePreviewViewEnhanced(): void {
    const isCurrentlyActive = this.isPreviewViewEnhanced$.value;
    this.isPreviewViewEnhanced$.next(!isCurrentlyActive);

    if (!isCurrentlyActive) {
      this.isCodeViewEnhanced$.next(false);
      this.isArtifactsViewEnhanced$.next(false);
      this.currentTab.set('preview');
    }
  }

  handleEnhancedPreviewTabClick(): void {
    this.startTabTransition('preview');
    this.activatePreviewTab();
    this.isPreviewViewEnhanced$.next(true);
    this.isCodeViewEnhanced$.next(false);
    this.isArtifactsViewEnhanced$.next(false);
    this.completeTabTransition();
  }

  handleEnhancedCodeTabClick(): void {
    this.startTabTransition('code');
    this.activateCodeTab();
    this.isCodeViewEnhanced$.next(true);
    this.isPreviewViewEnhanced$.next(false);
    this.isArtifactsViewEnhanced$.next(false);
    this.completeTabTransition();
  }

  private activateCodeTab(): void {
    this.isCodeActive$.next(true);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(false);
    this.isLogsActive$.next(false);
    this.isOverviewActive$.next(false);
    this.currentView$.next('editor');
    this.currentTab.set('code');
  }

  private activatePreviewTab(): void {
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);
    this.isArtifactsActive$.next(false);
    this.isLogsActive$.next(false);
    this.isOverviewActive$.next(false);
    this.currentView$.next('preview');
    this.currentTab.set('preview');
  }

  private activateArtifactsTab(): void {
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(true);
    this.isLogsActive$.next(false);
    this.isOverviewActive$.next(false);
    this.currentView$.next('artifacts');
    this.currentTab.set('artifacts');
  }

  private activateLogsTab(): void {
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(false);
    this.isLogsActive$.next(true);
    this.isOverviewActive$.next(false);
    this.currentView$.next('logs');
    this.currentTab.set('logs');
  }

  private activateOverviewTab(): void {
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(false);
    this.isArtifactsActive$.next(false);
    this.isLogsActive$.next(false);
    this.isOverviewActive$.next(true);
    this.currentView$.next('overview');
    this.currentTab.set('overview');
  }

  private startTabTransition(targetTab: string): void {
    this.isTransitioning.set(true);

    switch (targetTab.toLowerCase()) {
      case 'code':
      case 'editor':
        this.isCodeTabTransitioning$.next(true);
        break;
      case 'preview':
        this.isPreviewTabTransitioning$.next(true);
        break;
      case 'artifacts':
        this.isArtifactsTabTransitioning$.next(true);
        break;
    }
  }

  private completeTabTransition(): void {
    setTimeout(() => {
      this.isTransitioning.set(false);
      this.isCodeTabTransitioning$.next(false);
      this.isPreviewTabTransitioning$.next(false);
      this.isArtifactsTabTransitioning$.next(false);
    }, 200);
  }

  resetToDefaultState(): void {
    this.currentTab.set('preview');
    this.isTransitioning.set(false);
    this.userSelectedTab.set(false);
    this.activatePreviewTab();

    this.isCodeViewEnhanced$.next(false);
    this.isPreviewViewEnhanced$.next(true);
    this.isArtifactsViewEnhanced$.next(false);

    this.isCodeTabTransitioning$.next(false);
    this.isPreviewTabTransitioning$.next(false);
    this.isArtifactsTabTransitioning$.next(false);
  }

  getCurrentTabState(): {
    currentTab: string;
    currentView: string;
    isTransitioning: boolean;
    activeStates: {
      code: boolean;
      preview: boolean;
      artifacts: boolean;
      logs: boolean;
      overview: boolean;
    };
  } {
    return {
      currentTab: this.currentTab(),
      currentView: this.currentView$.value,
      isTransitioning: this.isTransitioning(),
      activeStates: {
        code: this.isCodeActive$.value,
        preview: this.isPreviewActive$.value,
        artifacts: this.isArtifactsActive$.value,
        logs: this.isLogsActive$.value,
        overview: this.isOverviewActive$.value,
      }
    };
  }
}
