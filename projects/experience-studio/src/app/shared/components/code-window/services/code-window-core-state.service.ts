import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { FileModel } from '../../code-viewer/code-viewer.component';

export type ViewType = 'loading' | 'editor' | 'preview' | 'overview' | 'logs' | 'artifacts';
export type ThemeType = 'light' | 'dark';

export interface TabTransitionState {
  activeTab: string;
  isTransitioning: boolean;
  lastError: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowCoreStateService {
  // Core state
  private files$ = new BehaviorSubject<FileModel[]>([]);
  private isResizing$ = new BehaviorSubject<boolean>(false);
  private currentView$ = new BehaviorSubject<ViewType>('preview');
  private isLoading$ = new BehaviorSubject<boolean>(true);
  private currentTheme$ = new BehaviorSubject<ThemeType>('light');
  
  // Panel state
  private isPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private isLeftPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private minWidth$ = new BehaviorSubject<string>('300px');
  private shouldHideProjectName$ = new BehaviorSubject<boolean>(false);
  private isExperienceStudioModalOpen$ = new BehaviorSubject<boolean>(false);

  // Tab state
  private isHistoryActive$ = new BehaviorSubject<boolean>(false);
  private isCodeActive$ = new BehaviorSubject<boolean>(false);
  private isPreviewActive$ = new BehaviorSubject<boolean>(true);
  private isArtifactsActive$ = new BehaviorSubject<boolean>(false);
  private tabTransitionInProgress$ = new BehaviorSubject<boolean>(false);
  private currentTabState$ = new BehaviorSubject<TabTransitionState>({
    activeTab: 'preview',
    isTransitioning: false,
    lastError: null
  });

  // Public observables
  readonly files = this.files$.asObservable();
  readonly isResizing = this.isResizing$.asObservable();
  readonly currentView = this.currentView$.asObservable();
  readonly isLoading = this.isLoading$.asObservable();
  readonly currentTheme = this.currentTheme$.asObservable();
  readonly isPanelCollapsed = this.isPanelCollapsed$.asObservable();
  readonly isLeftPanelCollapsed = this.isLeftPanelCollapsed$.asObservable();
  readonly minWidth = this.minWidth$.asObservable();
  readonly shouldHideProjectName = this.shouldHideProjectName$.asObservable();
  readonly isExperienceStudioModalOpen = this.isExperienceStudioModalOpen$.asObservable();
  readonly isHistoryActive = this.isHistoryActive$.asObservable();
  readonly isCodeActive = this.isCodeActive$.asObservable();
  readonly isPreviewActive = this.isPreviewActive$.asObservable();
  readonly isArtifactsActive = this.isArtifactsActive$.asObservable();
  readonly tabTransitionInProgress = this.tabTransitionInProgress$.asObservable();
  readonly currentTabState = this.currentTabState$.asObservable();

  // State update methods
  setFiles(files: FileModel[]): void {
    this.files$.next(files);
  }

  getCurrentFiles(): FileModel[] {
    return this.files$.value;
  }

  setCurrentView(view: ViewType): void {
    this.currentView$.next(view);
  }

  getCurrentView(): ViewType {
    return this.currentView$.value;
  }

  setLoading(loading: boolean): void {
    this.isLoading$.next(loading);
  }

  setTheme(theme: ThemeType): void {
    this.currentTheme$.next(theme);
  }

  getCurrentTheme(): ThemeType {
    return this.currentTheme$.value;
  }

  togglePanelCollapsed(): void {
    this.isPanelCollapsed$.next(!this.isPanelCollapsed$.value);
  }

  setPanelCollapsed(collapsed: boolean): void {
    this.isPanelCollapsed$.next(collapsed);
  }

  toggleLeftPanel(): void {
    this.isLeftPanelCollapsed$.next(!this.isLeftPanelCollapsed$.value);
  }

  setLeftPanelCollapsed(collapsed: boolean): void {
    this.isLeftPanelCollapsed$.next(collapsed);
  }

  setShouldHideProjectName(hide: boolean): void {
    this.shouldHideProjectName$.next(hide);
  }

  setResizing(resizing: boolean): void {
    this.isResizing$.next(resizing);
  }

  setExperienceStudioModalOpen(open: boolean): void {
    this.isExperienceStudioModalOpen$.next(open);
  }

  // Tab state methods
  setHistoryActive(active: boolean): void {
    this.isHistoryActive$.next(active);
  }

  setCodeActive(active: boolean): void {
    this.isCodeActive$.next(active);
  }

  setPreviewActive(active: boolean): void {
    this.isPreviewActive$.next(active);
  }

  setArtifactsActive(active: boolean): void {
    this.isArtifactsActive$.next(active);
  }

  setTabTransitionInProgress(inProgress: boolean): void {
    this.tabTransitionInProgress$.next(inProgress);
  }

  updateTabState(state: Partial<TabTransitionState>): void {
    const currentState = this.currentTabState$.value;
    this.currentTabState$.next({ ...currentState, ...state });
  }

  setCurrentTabState(state: TabTransitionState): void {
    this.currentTabState$.next(state);
  }

  getCurrentTabState(): TabTransitionState {
    return this.currentTabState$.value;
  }

  // Tab switching helper methods
  activateTab(tab: 'preview' | 'code' | 'artifacts' | 'history' | 'overview'): void {
    // Reset all tabs
    this.setPreviewActive(false);
    this.setCodeActive(false);
    this.setArtifactsActive(false);
    this.setHistoryActive(false);

    // Activate selected tab
    switch (tab) {
      case 'preview':
        this.setPreviewActive(true);
        this.setCurrentView('preview');
        break;
      case 'code':
        this.setCodeActive(true);
        this.setCurrentView('editor');
        break;
      case 'artifacts':
        this.setArtifactsActive(true);
        this.setCurrentView('artifacts');
        break;
      case 'history':
        this.setHistoryActive(true);
        break;
      case 'overview':
        this.setCurrentView('overview');
        break;
    }
  }

  // Reset methods
  reset(): void {
    this.files$.next([]);
    this.isResizing$.next(false);
    this.currentView$.next('preview');
    this.isLoading$.next(true);
    this.isPanelCollapsed$.next(false);
    this.isLeftPanelCollapsed$.next(false);
    this.shouldHideProjectName$.next(false);
    this.isExperienceStudioModalOpen$.next(false);
    this.isHistoryActive$.next(false);
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);
    this.isArtifactsActive$.next(false);
    this.tabTransitionInProgress$.next(false);
    this.currentTabState$.next({
      activeTab: 'preview',
      isTransitioning: false,
      lastError: null
    });
  }

  resetTabState(): void {
    this.isHistoryActive$.next(false);
    this.isCodeActive$.next(false);
    this.isPreviewActive$.next(true);
    this.isArtifactsActive$.next(false);
    this.tabTransitionInProgress$.next(false);
    this.currentTabState$.next({
      activeTab: 'preview',
      isTransitioning: false,
      lastError: null
    });
  }

  // Complete tab transition
  completeTabTransition(): void {
    this.setTabTransitionInProgress(false);
  }

  // Get left panel collapsed state
  getLeftPanelCollapsed(): boolean {
    return this.isLeftPanelCollapsed$.value;
  }

  // Modal state management
  getExperienceStudioModalOpen(): boolean {
    return this.isExperienceStudioModalOpen$.value;
  }

  toggleExperienceStudioModal(): void {
    this.isExperienceStudioModalOpen$.next(!this.isExperienceStudioModalOpen$.value);
  }
}
