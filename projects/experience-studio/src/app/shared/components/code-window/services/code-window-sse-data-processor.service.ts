import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject, Subscription } from 'rxjs';
import { takeUntil, filter, take } from 'rxjs/operators';
import { FileModel } from '../../code-viewer/code-viewer.component';
import { CodeWindowUIDesignStateService } from './code-window-ui-design-state.service';
import { CodeWindowPreviewStateService } from './code-window-preview-state.service';


export interface SSEDataProcessorCallbacks {
  updateCodeViewer: (files: any) => void;
  updatePreviewWindow: (url: string) => void;
  updateArtifactsWindow: (data: any) => void;
  handleAutomaticPreviewTabSwitch: () => void;
  showDeploymentErrorInPreview: () => void;
  setCodeGenerationComplete: () => void;
  setViewingSeedProjectTemplate: (value: boolean) => void;
  handleAutomaticCodeTabSwitch: () => void;
}

export interface SSEProcessingState {
  isProcessing: boolean;
  lastCodeFiles: any[] | null;
  lastPreviewUrl: string | null;
  lastArtifactData: any | null;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowSSEDataProcessorService {
  private processingState$ = new BehaviorSubject<SSEProcessingState>({
    isProcessing: false,
    lastCodeFiles: null,
    lastPreviewUrl: null,
    lastArtifactData: null
  });

  // Events
  private codeFilesProcessed$ = new Subject<{ files: any[]; fileModels: FileModel[] }>();
  private previewUrlProcessed$ = new Subject<{ url: string; isValid: boolean }>();
  private artifactDataProcessed$ = new Subject<{ data: any; isValid: boolean }>();

  // Public observables
  readonly processingState = this.processingState$.asObservable();
  readonly codeFilesProcessed = this.codeFilesProcessed$.asObservable();
  readonly previewUrlProcessed = this.previewUrlProcessed$.asObservable();
  readonly artifactDataProcessed = this.artifactDataProcessed$.asObservable();

  constructor(
    private uiDesignStateService: CodeWindowUIDesignStateService,
    private previewStateService: CodeWindowPreviewStateService
    // fileOperationsService removed as it's not used
  ) {}

  // Subscribe to SSE data processor
  subscribeToSSEDataProcessor(
    sseDataProcessor: any,
    sseDataProcessorSubscription: Subscription,
    destroy$: Subject<void>,
    callbacks: SSEDataProcessorCallbacks
  ): void {
    this.processingState$.next({
      ...this.processingState$.value,
      isProcessing: true
    });

    // Code files subscription
    sseDataProcessorSubscription.add(
      sseDataProcessor.codeFiles$.pipe(
        takeUntil(destroy$),
        filter((codeFiles: any) => {
          const isUIDesignMode = this.uiDesignStateService.getUIDesignMode();
          let isRegenerationInProgress = false;
          this.previewStateService.isRegenerationInProgress.pipe(take(1)).subscribe(val => isRegenerationInProgress = val);

          const shouldProcess = codeFiles && codeFiles.length > 0 &&
            !isUIDesignMode &&
            !isRegenerationInProgress;

          if (!shouldProcess && codeFiles && codeFiles.length > 0) {
            // Processing blocked due to UI Design mode or regeneration in progress
          }
          return shouldProcess;
        })
      ).subscribe((codeFiles: any) => {
        this.processCodeFiles(codeFiles, callbacks);
      })
    );

    // Preview URL subscription
    sseDataProcessorSubscription.add(
      sseDataProcessor.previewUrl$.pipe(
        takeUntil(destroy$),
        filter(() => {
          const isUIDesignMode = this.uiDesignStateService.getUIDesignMode();
          let isRegenerationInProgress = false;
          this.previewStateService.isRegenerationInProgress.pipe(take(1)).subscribe(val => isRegenerationInProgress = val);

          const shouldProcess = !isUIDesignMode && !isRegenerationInProgress;
          return shouldProcess;
        })
      ).subscribe((url: any) => {
        this.processPreviewUrl(url, callbacks);
      })
    );

    // Artifact data subscription
    sseDataProcessorSubscription.add(
      sseDataProcessor.artifactData$.pipe(
        takeUntil(destroy$),
        filter(() => {
          const isUIDesignMode = this.uiDesignStateService.getUIDesignMode();
          let isRegenerationInProgress = false;
          this.previewStateService.isRegenerationInProgress.pipe(take(1)).subscribe(val => isRegenerationInProgress = val);

          const shouldProcess = !isUIDesignMode && !isRegenerationInProgress;
          return shouldProcess;
        })
      ).subscribe((artifactData: any) => {
        this.processArtifactData(artifactData, callbacks);
      })
    );
  }

  // Process code files
  private processCodeFiles(codeFiles: any[], callbacks: SSEDataProcessorCallbacks): void {
    try {
      const fileModels = codeFiles.map(file => ({
        name: file.path,
        type: 'file' as const,
        content: file.code,
      }));

      // Update state
      this.processingState$.next({
        ...this.processingState$.value,
        lastCodeFiles: codeFiles
      });

      // Update component state through callbacks
      callbacks.setViewingSeedProjectTemplate(false);
      callbacks.setCodeGenerationComplete();
      callbacks.updateCodeViewer(codeFiles);
      callbacks.handleAutomaticCodeTabSwitch();

      // Emit event
      this.codeFilesProcessed$.next({ files: codeFiles, fileModels });

    } catch (error) {
      // Error processing code files
    }
  }

  // Process preview URL
  private processPreviewUrl(url: string, callbacks: SSEDataProcessorCallbacks): void {
    try {
      const isValidUrl: boolean = !!(url && url.trim() !== '' && url !== 'ERROR_DEPLOYMENT_FAILED');

      // Update state
      this.processingState$.next({
        ...this.processingState$.value,
        lastPreviewUrl: url
      });

      if (isValidUrl) {
        callbacks.updatePreviewWindow(url);
        callbacks.handleAutomaticPreviewTabSwitch();
      } else if (url === 'ERROR_DEPLOYMENT_FAILED') {
        callbacks.showDeploymentErrorInPreview();
      }

      // Emit event
      this.previewUrlProcessed$.next({ url, isValid: isValidUrl });

    } catch (error) {
      // Error processing preview URL
    }
  }

  // Process artifact data
  private processArtifactData(artifactData: any, callbacks: SSEDataProcessorCallbacks): void {
    try {
      const isValidData = artifactData !== null && artifactData !== undefined;

      // Update state
      this.processingState$.next({
        ...this.processingState$.value,
        lastArtifactData: artifactData
      });

      if (isValidData) {
        callbacks.updateArtifactsWindow(artifactData);
      }

      // Emit event
      this.artifactDataProcessed$.next({ data: artifactData, isValid: isValidData });

    } catch (error) {
      // Error processing artifact data
    }
  }

  // Check if processing is active
  isProcessing(): boolean {
    return this.processingState$.value.isProcessing;
  }

  // Get last processed data
  getLastCodeFiles(): any[] | null {
    return this.processingState$.value.lastCodeFiles;
  }

  getLastPreviewUrl(): string | null {
    return this.processingState$.value.lastPreviewUrl;
  }

  getLastArtifactData(): any | null {
    return this.processingState$.value.lastArtifactData;
  }

  // Reset processing state
  reset(): void {
    this.processingState$.next({
      isProcessing: false,
      lastCodeFiles: null,
      lastPreviewUrl: null,
      lastArtifactData: null
    });
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.codeFilesProcessed$.complete();
    this.previewUrlProcessed$.complete();
    this.artifactDataProcessed$.complete();
  }
}
