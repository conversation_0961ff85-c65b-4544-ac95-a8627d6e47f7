import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { CodeWindowPreviewStateService } from './code-window-preview-state.service';
import { CodeWindowCoreStateService } from './code-window-core-state.service';

export interface RegenerationState {
  isInProgress: boolean;
  startTime: number;
  sessionId: string | null;
  lastUserRequest: string | null;
  progressDescription: string;
  hasTimeout: boolean;
  timeoutId: any;
}

export interface RegenerationPayload {
  userRequest: string;
  sessionId: string;
  aiMessageId: string;
  regenerationType: 'direct' | 'sequential';
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowRegenerationService {
  private regenerationState$ = new BehaviorSubject<RegenerationState>({
    isInProgress: false,
    startTime: 0,
    sessionId: null,
    lastUserRequest: null,
    progressDescription: '',
    hasTimeout: false,
    timeoutId: null
  });

  private regenerationTimeout: any = null;
  private readonly REGENERATION_TIMEOUT_MS = 300000; // 5 minutes

  // Events
  private regenerationStarted$ = new Subject<RegenerationPayload>();
  private regenerationCompleted$ = new Subject<{ success: boolean; data?: any; error?: any }>();
  private regenerationFailed$ = new Subject<{ error: any; sessionId: string }>();

  // Public observables
  readonly regenerationState = this.regenerationState$.asObservable();
  readonly regenerationStarted = this.regenerationStarted$.asObservable();
  readonly regenerationCompleted = this.regenerationCompleted$.asObservable();
  readonly regenerationFailed = this.regenerationFailed$.asObservable();

  constructor(
    private previewStateService: CodeWindowPreviewStateService,
    private coreStateService: CodeWindowCoreStateService
  ) {}

  // Start regeneration workflow
  startRegeneration(payload: RegenerationPayload): void {
    const currentState = this.regenerationState$.value;
    
    if (currentState.isInProgress) {
      return;
    }

    // Update state
    this.regenerationState$.next({
      isInProgress: true,
      startTime: Date.now(),
      sessionId: payload.sessionId,
      lastUserRequest: payload.userRequest,
      progressDescription: 'Starting regeneration...',
      hasTimeout: false,
      timeoutId: null
    });

    // Update preview state
    this.previewStateService.setRegenerationInProgress(true);
    this.previewStateService.setCodeGenerationLoading(true);

    // Start timeout
    this.startRegenerationTimeout();

    // Emit event
    this.regenerationStarted$.next(payload);
  }

  // Complete regeneration successfully
  completeRegeneration(data?: any): void {
    const currentState = this.regenerationState$.value;
    
    if (!currentState.isInProgress) {
      return;
    }

    // Clear timeout
    this.clearRegenerationTimeout();

    // Update state
    this.regenerationState$.next({
      ...currentState,
      isInProgress: false,
      progressDescription: 'Regeneration completed successfully'
    });

    // Update preview state
    this.previewStateService.setRegenerationInProgress(false);
    this.previewStateService.setCodeGenerationLoading(false);

    // Emit event
    this.regenerationCompleted$.next({ success: true, data });
  }

  // Handle regeneration failure
  failRegeneration(error: any): void {
    const currentState = this.regenerationState$.value;
    
    if (!currentState.isInProgress) {
      return;
    }

    // Clear timeout
    this.clearRegenerationTimeout();

    // Update state
    this.regenerationState$.next({
      ...currentState,
      isInProgress: false,
      progressDescription: `Regeneration failed: ${error?.message || 'Unknown error'}`
    });

    // Update preview state
    this.previewStateService.setRegenerationInProgress(false);
    this.previewStateService.setCodeGenerationLoading(false);

    // Emit events
    this.regenerationFailed$.next({ 
      error, 
      sessionId: currentState.sessionId || '' 
    });
    this.regenerationCompleted$.next({ success: false, error });
  }

  // Update progress description
  updateProgress(description: string): void {
    const currentState = this.regenerationState$.value;
    this.regenerationState$.next({
      ...currentState,
      progressDescription: description
    });
  }

  // Generate session ID
  generateSessionId(): string {
    return `regen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Check if regeneration is in progress
  isRegenerationInProgress(): boolean {
    return this.regenerationState$.value.isInProgress;
  }

  // Get current regeneration state
  getCurrentState(): RegenerationState {
    return this.regenerationState$.value;
  }

  // Start regeneration timeout
  private startRegenerationTimeout(): void {
    this.clearRegenerationTimeout();
    
    this.regenerationTimeout = setTimeout(() => {
      const currentState = this.regenerationState$.value;
      
      if (currentState.isInProgress) {
        this.regenerationState$.next({
          ...currentState,
          hasTimeout: true,
          progressDescription: 'Regeneration timed out'
        });

        this.failRegeneration(new Error('Regeneration timed out after 5 minutes'));
      }
    }, this.REGENERATION_TIMEOUT_MS);

    // Update state to track timeout
    const currentState = this.regenerationState$.value;
    this.regenerationState$.next({
      ...currentState,
      timeoutId: this.regenerationTimeout
    });
  }

  // Clear regeneration timeout
  private clearRegenerationTimeout(): void {
    if (this.regenerationTimeout) {
      clearTimeout(this.regenerationTimeout);
      this.regenerationTimeout = null;
    }

    const currentState = this.regenerationState$.value;
    if (currentState.timeoutId) {
      this.regenerationState$.next({
        ...currentState,
        hasTimeout: false,
        timeoutId: null
      });
    }
  }

  // Reset regeneration state
  reset(): void {
    this.clearRegenerationTimeout();
    
    this.regenerationState$.next({
      isInProgress: false,
      startTime: 0,
      sessionId: null,
      lastUserRequest: null,
      progressDescription: '',
      hasTimeout: false,
      timeoutId: null
    });

    this.previewStateService.setRegenerationInProgress(false);
    this.previewStateService.setCodeGenerationLoading(false);
  }

  // Validate regeneration state before starting
  validateAndResetRegenerationState(): boolean {
    const currentState = this.regenerationState$.value;
    
    // If regeneration has been running for more than 10 minutes, force reset
    if (currentState.isInProgress &&
        currentState.startTime > 0 &&
        (Date.now() - currentState.startTime) > 600000) {

      this.reset();
      return true;
    }

    return !currentState.isInProgress;
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.clearRegenerationTimeout();
    this.regenerationStarted$.complete();
    this.regenerationCompleted$.complete();
    this.regenerationFailed$.complete();
  }
}
  