import { Injectable, signal, computed, inject, DestroyRef, NgZone, ChangeDetectorRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable } from 'rxjs';
import { SafeHtml } from '@angular/platform-browser';
import { UIDesignSelectionService, MultiSelectedNodeData } from '../../../services/ui-design-selection.service';
import { UIDesignVisualFeedbackService } from '../../../services/ui-design-visual-feedback.service';
import { UIDesignNode } from './ui-design-node.service';

export interface NodeOperationResult {
  success: boolean;
  affectedNodes: string[];
  operation: string;
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowNodeOperationsService {
  private destroyRef = inject(DestroyRef);
  private ngZone = inject(NgZone);
  private uiDesignSelectionService = inject(UIDesignSelectionService);
  private uiDesignVisualFeedbackService = inject(UIDesignVisualFeedbackService);

  readonly selectedNodeCount = signal<number>(0);
  readonly lastNodeOperation = signal<string>('');
  readonly nodeOperationCount = signal<number>(0);
  readonly isNodeOperationInProgress = signal<boolean>(false);

  private uiDesignNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private selectedUIDesignNode$ = new BehaviorSubject<UIDesignNode | null>(null);
  private nodeOperationHistory$ = new BehaviorSubject<NodeOperationResult[]>([]);

  private regenerationSessionCounter = 0;

  readonly uiDesignNodes = this.uiDesignNodes$.asObservable();
  readonly selectedUIDesignNode = this.selectedUIDesignNode$.asObservable();
  readonly nodeOperationHistory = this.nodeOperationHistory$.asObservable();

  readonly hasNodes = computed(() => this.uiDesignNodes$.value.length > 0);
  readonly hasSelectedNodes = computed(() => this.selectedNodeCount() > 0);
  readonly canPerformNodeOperations = computed(() =>
    this.hasNodes() && !this.isNodeOperationInProgress()
  );

  constructor() {
    this.initializeNodeOperations();
  }

  handleUIDesignNodeSelect(node: UIDesignNode, event?: MouseEvent, cdr?: ChangeDetectorRef): void {
    if (!node) {
      return;
    }

    this.ngZone.run(() => {
      this.isNodeOperationInProgress.set(true);

      const isMultiSelect = event && (event.ctrlKey || event.metaKey || event.shiftKey);

      const selectionData: MultiSelectedNodeData = {
        nodeId: node.id,
        fileName: node.data.displayTitle || node.data.title,
        htmlContent: node.data.htmlContent as string,
        rawContent: node.data.rawContent,
        selectedImages: [],
        metadata: {
          nodePosition: node.position,
          nodeDimensions: { width: node.data.width, height: node.data.height },
          selectionTimestamp: Date.now(),
        },
      };

      this.uiDesignSelectionService.selectMultipleNodes(
        node.id,
        selectionData,
        isMultiSelect || false
      );

      this.uiDesignVisualFeedbackService.toggleNodeSelection(node.id, isMultiSelect || false);

      this.selectedUIDesignNode$.next(node);
      this.selectedNodeCount.set(this.uiDesignSelectionService.getSelectedNodesCount());
      this.nodeOperationCount.update(count => count + 1);
      this.lastNodeOperation.set(`select-${node.id}`);

      this.logNodeOperation({
        success: true,
        affectedNodes: [node.id],
        operation: 'node-select',
        timestamp: Date.now()
      });

      this.isNodeOperationInProgress.set(false);

      if (cdr) {
        cdr.markForCheck();
      }
    });
  }

  updateNodeSelectionVisuals(cdr?: ChangeDetectorRef): void {
    if (cdr) {
      cdr.markForCheck();
    }

    this.lastNodeOperation.set('update-visuals');
  }

  clearNodeSelection(cdr?: ChangeDetectorRef): void {
    this.ngZone.run(() => {
      this.isNodeOperationInProgress.set(true);

      this.uiDesignSelectionService.clearSelection();

      this.uiDesignVisualFeedbackService.clearSelection();

      const currentNodes = this.uiDesignNodes$.value;
      const updatedNodes = currentNodes.map(node => ({
        ...node,
        selected: false,
      }));

      this.uiDesignNodes$.next(updatedNodes);
      this.selectedUIDesignNode$.next(null);
      this.selectedNodeCount.set(0);
      this.lastNodeOperation.set('clear-selection');

      this.logNodeOperation({
        success: true,
        affectedNodes: currentNodes.map(n => n.id),
        operation: 'clear-selection',
        timestamp: Date.now()
      });

      this.isNodeOperationInProgress.set(false);

      if (cdr) {
        cdr.markForCheck();
      }
    });
  }

  selectAllNodes(): void {
    const currentNodes = this.uiDesignNodes$.value;
    if (currentNodes.length === 0) {
      return;
    }

    this.ngZone.run(() => {
      this.isNodeOperationInProgress.set(true);

      const allNodesData: MultiSelectedNodeData[] = currentNodes.map(node => ({
        nodeId: node.id,
        fileName: node.data.displayTitle || node.data.title,
        htmlContent: node.data.htmlContent as string,
        rawContent: node.data.rawContent,
        selectedImages: [],
        metadata: {
          nodePosition: node.position,
          nodeDimensions: { width: node.data.width, height: node.data.height },
          selectionTimestamp: Date.now(),
        },
      }));

      this.uiDesignSelectionService.selectAllNodes(allNodesData);
      this.selectedNodeCount.set(currentNodes.length);
      this.lastNodeOperation.set('select-all');

      this.logNodeOperation({
        success: true,
        affectedNodes: currentNodes.map(n => n.id),
        operation: 'select-all',
        timestamp: Date.now()
      });

      this.isNodeOperationInProgress.set(false);
    });
  }

  clearAllSelection(): void {
    this.uiDesignSelectionService.clearSelection();
    this.selectedNodeCount.set(0);
    this.lastNodeOperation.set('clear-all');
  }

  getSelectedNodesCount(): number {
    return this.uiDesignSelectionService.getSelectedNodesCount();
  }

  generateRegenerationSessionId(): string {
    this.regenerationSessionCounter++;
    return `regen-session-${Date.now()}-${this.regenerationSessionCounter}-${Math.random().toString(36).substring(2, 11)}`;
  }

  trackByUIDesignNode(_index: number, node: UIDesignNode): string {
    return node.id;
  }

  getLoadingText(title: string): string {
    if (title.includes('Editing') && title.includes('...')) {
      return title;
    }
    return 'Generating wireframe...';
  }

  updateNodes(nodes: UIDesignNode[]): void {
    this.uiDesignNodes$.next(nodes);
    this.nodeOperationCount.update(count => count + 1);
    this.lastNodeOperation.set('update-nodes');
  }

  getCurrentNodes(): UIDesignNode[] {
    return this.uiDesignNodes$.value;
  }

  getSelectedNode(): UIDesignNode | null {
    return this.selectedUIDesignNode$.value;
  }

  isNodeSelected(nodeId: string): boolean {
    const selectedNodes = this.uiDesignSelectionService.getSelectedNodes();
    return selectedNodes.some(node => node.nodeId === nodeId);
  }

  private logNodeOperation(result: NodeOperationResult): void {
    const currentHistory = this.nodeOperationHistory$.value;
    const newHistory = [...currentHistory, result].slice(-100);
    this.nodeOperationHistory$.next(newHistory);
  }

  private initializeNodeOperations(): void {
    this.destroyRef.onDestroy(() => {
      this.clearAllSelection();
    });

  }

  getCurrentNodeState(): {
    signals: {
      selectedCount: number;
      hasNodes: boolean;
      hasSelected: boolean;
      canPerformOperations: boolean;
      operationCount: number;
    };
    nodes: {
      total: number;
      selected: number;
      lastOperation: string;
    };
    operationHistory: number;
  } {
    return {
      signals: {
        selectedCount: this.selectedNodeCount(),
        hasNodes: this.hasNodes(),
        hasSelected: this.hasSelectedNodes(),
        canPerformOperations: this.canPerformNodeOperations(),
        operationCount: this.nodeOperationCount(),
      },
      nodes: {
        total: this.uiDesignNodes$.value.length,
        selected: this.selectedNodeCount(),
        lastOperation: this.lastNodeOperation(),
      },
      operationHistory: this.nodeOperationHistory$.value.length
    };
  }
}
