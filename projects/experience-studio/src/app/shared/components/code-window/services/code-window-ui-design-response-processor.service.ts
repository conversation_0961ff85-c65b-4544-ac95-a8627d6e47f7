import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { UIDesignNode } from './ui-design-node.service';
import { CodeWindowUIDesignStateService } from './code-window-ui-design-state.service';
import { CodeWindowCoreStateService } from './code-window-core-state.service';

export interface UIDesignPageData {
  fileName: string;
  content: string;
}

export interface MobilePage {
  fileName: string;
  content: string;
}

export interface UIDesignAPIResponse {
  fileName?: string;
  pageName?: string;
  content: string;
}

export interface WireframeAPIResponse {
  fileName?: string;
  pageName?: string;
  content: string;
}

export interface UIDesignProcessingResult {
  success: boolean;
  pages?: UIDesignPageData[];
  nodes?: UIDesignNode[];
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowUIDesignResponseProcessorService {
  private processingState$ = new BehaviorSubject<{
    isProcessing: boolean;
    lastResult: UIDesignProcessingResult | null;
  }>({
    isProcessing: false,
    lastResult: null
  });

  // Events
  private responseProcessed$ = new Subject<UIDesignProcessingResult>();
  private processingFailed$ = new Subject<{ error: any; response: any }>();

  // Public observables
  readonly processingState = this.processingState$.asObservable();
  readonly responseProcessed = this.responseProcessed$.asObservable();
  readonly processingFailed = this.processingFailed$.asObservable();

  constructor(
    private uiDesignStateService: CodeWindowUIDesignStateService,
    private coreStateService: CodeWindowCoreStateService
  ) {}

  // Process UI Design response
  processUIDesignResponse(
    response: string | UIDesignAPIResponse[] | WireframeAPIResponse[],
    callbacks: {
      clearAllLoadingNodes: () => void;
      showUIDesignError: (message: string) => void;
      extractPageNameFromFileName: (fileName: string) => string;
      createUIDesignNodes: (pages: UIDesignPageData[]) => Promise<void>;
    }
  ): void {
    this.processingState$.next({
      isProcessing: true,
      lastResult: null
    });

    callbacks.clearAllLoadingNodes();

    try {
      let pages: any[] = [];

      if (typeof response === 'string') {
        try {
          pages = JSON.parse(response);
        } catch (parseError) {
          callbacks.showUIDesignError('Failed to parse response data');
          this.handleProcessingFailure('Parse error', response);
          return;
        }
      } else if (Array.isArray(response)) {
        pages = response;
      } else {
        callbacks.showUIDesignError('Invalid response format');
        this.handleProcessingFailure('Invalid format', response);
        return;
      }

      if (!Array.isArray(pages) || pages.length === 0) {
        callbacks.showUIDesignError('No pages found in response');
        this.handleProcessingFailure('No pages found', response);
        return;
      }

      const uiDesignPages: UIDesignPageData[] = pages.map((page, index) => {
        let pageName: string;
        let content: string;

        if ('fileName' in page) {
          pageName = callbacks.extractPageNameFromFileName(page.fileName);
          content = page.content;
        } else if ('pageName' in page) {
          pageName = page.pageName?.trim() || `Page ${index + 1}`;
          content = page.content;
        } else {
          pageName = `Page ${index + 1}`;
          content = page.content || '<html><body><p>No content available</p></body></html>';
        }

        return {
          fileName: pageName,
          content: content || '<html><body><p>No content available</p></body></html>',
        };
      });

      // Create nodes asynchronously
      callbacks.createUIDesignNodes(uiDesignPages).then(() => {
        const result: UIDesignProcessingResult = {
          success: true,
          pages: uiDesignPages
        };

        this.processingState$.next({
          isProcessing: false,
          lastResult: result
        });

        this.responseProcessed$.next(result);
      }).catch(error => {
        callbacks.showUIDesignError('Failed to create UI design nodes');
        this.handleProcessingFailure(error, response);
      });

    } catch (error) {
      callbacks.showUIDesignError('Failed to process response data');
      this.handleProcessingFailure(error, response);
    }
  }

  // Create UI Design nodes
  async createUIDesignNodes(
    pages: UIDesignPageData[],
    callbacks: {
      clearAllLoadingNodes: () => void;
      showUIDesignError: (message: string) => void;
      centerCanvasOnNodesWithViewport: (viewport: any) => void;
      updateUIDesignChatMessagesWithSummary: (summary: any) => void;
    },
    services: {
      wireframeNodeManagementService: any;
      uiDesignNodePositioningService: any;
      generateUIDesignService: any;
      uiDesignSelectionService: any;
      wireframeGenerationStateService: any;
    }
  ): Promise<void> {
    try {
      callbacks.clearAllLoadingNodes();

      const currentNodes = this.uiDesignStateService.getUIDesignNodes();

      const wireframePages = pages.map(page => ({
        fileName: page.fileName,
        content: page.content,
        pageName: page.fileName,
      }));

      const positionCalculator = (count: number, _existingNodes: any[]) => {
        const positioningResult =
          services.uiDesignNodePositioningService.calculateInitialGenerationPositions(count);
        return positioningResult.positions;
      };

      const result = await services.wireframeNodeManagementService.processRegenerationResponse(
        wireframePages,
        currentNodes,
        positionCalculator
      );

      this.uiDesignStateService.setUIDesignNodes(result.updatedNodes);
      this.uiDesignStateService.setUIDesignMode(true);
      this.coreStateService.setHistoryActive(false);

      if (result.updatedNodes.length > 0) {
        const nodePositions = result.updatedNodes.map((node: UIDesignNode) => node.position);
        const optimalViewport =
          services.uiDesignNodePositioningService.calculateOptimalViewport(nodePositions);

        setTimeout(() => {
          callbacks.centerCanvasOnNodesWithViewport(optimalViewport);
        }, 100);
      }

      const mobilePages: MobilePage[] = pages.map(page => ({
        fileName: page.fileName,
        content: page.content,
      }));

      const responseData = {
        pages: mobilePages,
        jobId: 'ui-design-' + Date.now(),
        projectId: 'ui-design-project-' + Date.now(),
      };

      services.generateUIDesignService.setUIDesignResponse(responseData);
      callbacks.updateUIDesignChatMessagesWithSummary(result.summary);
      services.uiDesignSelectionService.setNodesCreated(true);
      this.uiDesignStateService.setWireframeGenerationComplete(true);
      services.wireframeGenerationStateService.completeGeneration(result.updatedNodes.length);

    } catch (error) {
      callbacks.showUIDesignError(
        'Failed to create design nodes: ' +
        (error instanceof Error ? error.message : 'Unknown error')
      );
      throw error;
    }
  }

  // Check if response is UI Design response
  isUIDesignResponse(response: any): boolean {
    if (Array.isArray(response)) {
      return response.every(item => {
        if (typeof item !== 'object' || !item.content) {
          return false;
        }

        const hasPageName = 'pageName' in item;
        const hasFileName = 'fileName' in item;

        return hasPageName || hasFileName;
      });
    }

    if (typeof response === 'string') {
      try {
        const parsed = JSON.parse(response);
        return this.isUIDesignResponse(parsed);
      } catch {
        return false;
      }
    }

    return false;
  }

  // Handle processing failure
  private handleProcessingFailure(error: any, response: any): void {
    const result: UIDesignProcessingResult = {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };

    this.processingState$.next({
      isProcessing: false,
      lastResult: result
    });

    this.processingFailed$.next({ error, response });
  }

  // Check if processing is in progress
  isProcessing(): boolean {
    return this.processingState$.value.isProcessing;
  }

  // Get last processing result
  getLastResult(): UIDesignProcessingResult | null {
    return this.processingState$.value.lastResult;
  }

  // Reset service state
  reset(): void {
    this.processingState$.next({
      isProcessing: false,
      lastResult: null
    });
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.responseProcessed$.complete();
    this.processingFailed$.complete();
  }
}
