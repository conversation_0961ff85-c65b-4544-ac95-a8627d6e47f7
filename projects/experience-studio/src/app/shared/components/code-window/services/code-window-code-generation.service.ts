import { Injectable, NgZone, ChangeDetectorRef } from '@angular/core';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import { FileModel } from '../../code-viewer/code-viewer.component';
import { CodeWindowCoreStateService } from './code-window-core-state.service';
import { CodeWindowPreviewStateService } from './code-window-preview-state.service';
import { CodeWindowFileOperationsService } from './code-window-file-operations.service';
import { CodeWindowTabManagementService } from './code-window-tab-management.service';

export interface CodeGenerationOptions {
  userRequest: string;
  projectId: string;
  jobId: string;
  regenerationType?: 'direct' | 'sequential';
  imageDataUri?: string;
}

export interface CodeGenerationResult {
  success: boolean;
  files?: FileModel[];
  deployedUrl?: string;
  error?: any;
}

export interface CodeGenerationState {
  isInProgress: boolean;
  isComplete: boolean;
  startTime: number;
  progressDescription: string;
  currentPhase: string;
  hasError: boolean;
  errorMessage: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowCodeGenerationService {
  private generationState$ = new BehaviorSubject<CodeGenerationState>({
    isInProgress: false,
    isComplete: false,
    startTime: 0,
    progressDescription: '',
    currentPhase: 'idle',
    hasError: false,
    errorMessage: null
  });

  // Events
  private generationStarted$ = new Subject<CodeGenerationOptions>();
  private generationCompleted$ = new Subject<CodeGenerationResult>();
  private generationFailed$ = new Subject<{ error: any; options: CodeGenerationOptions }>();
  private progressUpdated$ = new Subject<{ phase: string; description: string }>();

  // Public observables
  readonly generationState = this.generationState$.asObservable();
  readonly generationStarted = this.generationStarted$.asObservable();
  readonly generationCompleted = this.generationCompleted$.asObservable();
  readonly generationFailed = this.generationFailed$.asObservable();
  readonly progressUpdated = this.progressUpdated$.asObservable();

  constructor(
    private coreStateService: CodeWindowCoreStateService,
    private previewStateService: CodeWindowPreviewStateService,
    private fileOperationsService: CodeWindowFileOperationsService,
    private tabManagementService: CodeWindowTabManagementService
  ) {}

  // Start code generation
  startCodeGeneration(options: CodeGenerationOptions): void {
    const currentState = this.generationState$.value;
    
    if (currentState.isInProgress) {
      console.warn('Code generation already in progress');
      return;
    }

    // Update state
    this.generationState$.next({
      isInProgress: true,
      isComplete: false,
      startTime: Date.now(),
      progressDescription: 'Starting code generation...',
      currentPhase: 'initialization',
      hasError: false,
      errorMessage: null
    });

    // Update core services
    this.showProgressIndicator();

    // Emit started event
    this.generationStarted$.next(options);
  }

  // Complete code generation successfully
  completeCodeGeneration(result: CodeGenerationResult): void {
    const currentState = this.generationState$.value;
    
    if (!currentState.isInProgress) {
      return;
    }

    // Update state
    this.generationState$.next({
      ...currentState,
      isInProgress: false,
      isComplete: true,
      progressDescription: 'Code generation completed successfully',
      currentPhase: 'completed',
      hasError: false,
      errorMessage: null
    });

    // Update core services
    this.showCompletionState();

    // Process files if provided
    if (result.files && result.files.length > 0) {
      this.fileOperationsService.processCodeFromService(result.files);
    }

    // Handle automatic tab switching
    this.tabManagementService.handleAutomaticCodeTabSwitch(
      // Note: NgZone and ChangeDetectorRef would need to be injected or passed
      // For now, we'll emit an event that the component can handle
    );

    // Emit completion event
    this.generationCompleted$.next(result);
  }

  // Handle code generation failure
  failCodeGeneration(error: any, options: CodeGenerationOptions): void {
    const currentState = this.generationState$.value;
    
    if (!currentState.isInProgress) {
      return;
    }

    const errorMessage = error?.message || error?.error?.message || 'Code generation failed';

    // Update state
    this.generationState$.next({
      ...currentState,
      isInProgress: false,
      isComplete: false,
      progressDescription: `Code generation failed: ${errorMessage}`,
      currentPhase: 'failed',
      hasError: true,
      errorMessage
    });

    // Update core services
    this.showErrorState();

    // Emit failure event
    this.generationFailed$.next({ error, options });
  }

  // Update progress
  updateProgress(phase: string, description: string): void {
    const currentState = this.generationState$.value;
    
    if (!currentState.isInProgress) {
      return;
    }

    this.generationState$.next({
      ...currentState,
      progressDescription: description,
      currentPhase: phase
    });

    // Emit progress event
    this.progressUpdated$.next({ phase, description });
  }

  // Show progress indicator
  private showProgressIndicator(): void {
    this.coreStateService.setLoading(true);
    this.previewStateService.setPreviewLoading(true);
    this.previewStateService.setPreviewError(false);
  }

  // Show completion state
  private showCompletionState(): void {
    this.coreStateService.setLoading(false);
    this.previewStateService.setPreviewLoading(false);
    this.previewStateService.setPreviewError(false);
  }

  // Show error state
  private showErrorState(): void {
    this.coreStateService.setLoading(false);
    this.previewStateService.setPreviewLoading(false);
    this.previewStateService.setPreviewError(true);
  }

  // Check if generation is in progress
  isGenerationInProgress(): boolean {
    return this.generationState$.value.isInProgress;
  }

  // Check if generation is complete
  isGenerationComplete(): boolean {
    return this.generationState$.value.isComplete;
  }

  // Get current generation state
  getCurrentState(): CodeGenerationState {
    return this.generationState$.value;
  }

  // Get current progress description
  getCurrentProgressDescription(): string {
    return this.generationState$.value.progressDescription;
  }

  // Get current phase
  getCurrentPhase(): string {
    return this.generationState$.value.currentPhase;
  }

  // Check if there's an error
  hasError(): boolean {
    return this.generationState$.value.hasError;
  }

  // Get error message
  getErrorMessage(): string | null {
    return this.generationState$.value.errorMessage;
  }

  // Reset generation state
  reset(): void {
    this.generationState$.next({
      isInProgress: false,
      isComplete: false,
      startTime: 0,
      progressDescription: '',
      currentPhase: 'idle',
      hasError: false,
      errorMessage: null
    });
  }

  // Cancel current generation
  cancelGeneration(): void {
    const currentState = this.generationState$.value;
    
    if (currentState.isInProgress) {
      this.generationState$.next({
        ...currentState,
        isInProgress: false,
        progressDescription: 'Code generation cancelled',
        currentPhase: 'cancelled'
      });

      this.coreStateService.setLoading(false);
      this.previewStateService.setPreviewLoading(false);
    }
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.generationStarted$.complete();
    this.generationCompleted$.complete();
    this.generationFailed$.complete();
    this.progressUpdated$.complete();
  }
}
