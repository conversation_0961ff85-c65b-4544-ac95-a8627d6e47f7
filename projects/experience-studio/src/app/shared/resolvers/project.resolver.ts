import { Injectable, inject } from '@angular/core';
import { Router, ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { Observable, of, catchError, map } from 'rxjs';
import { RecentProjectService, ProjectDetailResponse } from '../services/recent-project-services/recent-project.service';
import { ToastService } from '../services/toast.service';
import { createLogger } from '../utils';

/**
 * ENTERPRISE: Project resolver for loading project data before route activation
 * Ensures project exists and user has access before displaying the route
 */
export interface ProjectResolverData {
  project: ProjectDetailResponse | null;
  projectId: string;
  error?: string;
}

/**
 * Project resolver function using Angular 18+ functional approach
 */
export const projectResolver: ResolveFn<ProjectResolverData> = (
  route: ActivatedRouteSnapshot
): Observable<ProjectResolverData> => {
  const recentProjectService = inject(RecentProjectService);
  const router = inject(Router);
  const toastService = inject(ToastService);
  const logger = createLogger('ProjectResolver');

  const projectId = route.paramMap.get('projectId');
  
  if (!projectId) {
    logger.error('No project ID provided in route parameters');
    toastService.error('Invalid project URL');
    router.navigate(['/experience/main']);
    return of({ project: null, projectId: '', error: 'No project ID' });
  }

  // Validate UUID format (basic validation)
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(projectId)) {
    logger.warn('Invalid project ID format:', projectId);
    toastService.error('Invalid project ID format');
    router.navigate(['/experience/main']);
    return of({ project: null, projectId, error: 'Invalid project ID format' });
  }

  logger.info('Resolving project data for ID:', projectId);

  return recentProjectService.getProjectById(projectId).pipe(
    map(response => {
      if (response && response.project_details) {
        logger.info('Project resolved successfully:', response.project_details.project_name);
        return { project: response, projectId };
      } else {
        logger.warn('Project not found:', projectId);
        toastService.error('Project not found');
        router.navigate(['/experience/main']);
        return { project: null, projectId, error: 'Project not found' };
      }
    }),
    catchError(error => {
      logger.error('Error resolving project:', error);
      const errorMessage = error?.message || 'Failed to load project';
      toastService.error(`Failed to load project: ${errorMessage}`);
      router.navigate(['/experience/main']);
      return of({ project: null, projectId, error: errorMessage });
    })
  );
};

/**
 * ENTERPRISE: Project access guard resolver
 * Validates user permissions for project access
 */
export const projectAccessResolver: ResolveFn<boolean> = (
  route: ActivatedRouteSnapshot
): Observable<boolean> => {
  const logger = createLogger('ProjectAccessResolver');
  const toastService = inject(ToastService);
  const router = inject(Router);

  const projectId = route.paramMap.get('projectId');
  const action = route.data?.['routeType'] || 'view';

  logger.info('Checking project access:', { projectId, action });

  // TODO: Implement actual permission checking logic
  // For now, allow all access - replace with actual permission service
  
  // Example permission logic:
  // const userService = inject(UserService);
  // const permissionService = inject(PermissionService);
  // 
  // return permissionService.checkProjectAccess(projectId, userService.getCurrentUser(), action)
  //   .pipe(
  //     map(hasAccess => {
  //       if (!hasAccess) {
  //         toastService.error('You do not have permission to access this project');
  //         router.navigate(['/experience/main']);
  //         return false;
  //       }
  //       return true;
  //     }),
  //     catchError(() => {
  //       toastService.error('Failed to verify project access');
  //       router.navigate(['/experience/main']);
  //       return of(false);
  //     })
  //   );

  return of(true);
};

/**
 * ENTERPRISE: Project type resolver
 * Determines project type from route or project data
 */
export const projectTypeResolver: ResolveFn<'ui-design' | 'application' | 'unknown'> = (
  route: ActivatedRouteSnapshot
): Observable<'ui-design' | 'application' | 'unknown'> => {
  const logger = createLogger('ProjectTypeResolver');
  
  // First try to determine from route path
  const routePath = route.routeConfig?.path || '';
  
  if (routePath.includes('ui-design')) {
    logger.info('Project type determined from route: ui-design');
    return of('ui-design');
  } else if (routePath.includes('application')) {
    logger.info('Project type determined from route: application');
    return of('application');
  }

  // If not determinable from route, try from route data
  const cardType = route.data?.['cardType'];
  if (cardType === 'Generate UI Design') {
    logger.info('Project type determined from route data: ui-design');
    return of('ui-design');
  } else if (cardType === 'Generate Application') {
    logger.info('Project type determined from route data: application');
    return of('application');
  }

  // TODO: If still unknown, could resolve from project data
  // const projectId = route.paramMap.get('projectId');
  // if (projectId) {
  //   return recentProjectService.getProjectById(projectId).pipe(
  //     map(response => {
  //       const projectType = response?.project?.project_type?.toLowerCase();
  //       return projectType === 'ui-design' || projectType === 'application' 
  //         ? projectType as 'ui-design' | 'application'
  //         : 'unknown';
  //     }),
  //     catchError(() => of('unknown' as const))
  //   );
  // }

  logger.warn('Could not determine project type, defaulting to unknown');
  return of('unknown');
};

/**
 * ENTERPRISE: Route metadata resolver
 * Provides metadata for analytics and breadcrumb generation
 */
export interface RouteMetadata {
  title: string;
  description: string;
  breadcrumbs: Array<{label: string, route?: string}>;
  analytics: {
    category: string;
    action: string;
    label?: string;
  };
}

export const routeMetadataResolver: ResolveFn<RouteMetadata> = (
  route: ActivatedRouteSnapshot
): Observable<RouteMetadata> => {
  const logger = createLogger('RouteMetadataResolver');
  
  const projectId = route.paramMap.get('projectId');
  const routeType = route.data?.['routeType'] || 'unknown';
  const breadcrumbLabel = route.data?.['breadcrumb'] || 'Page';

  // Build breadcrumbs
  const breadcrumbs: Array<{label: string, route?: string}> = [
    { label: 'Home', route: '/experience/main' }
  ];

  // Determine page title and description based on route type
  let title = 'Experience Studio';
  let description = 'AI-powered development platform';
  let analyticsCategory = 'navigation';
  let analyticsAction = 'view';

  switch (routeType) {
    case 'ui-design-new':
      title = 'New UI Design';
      description = 'Create a new UI design with AI assistance';
      breadcrumbs.push({ label: 'UI Design', route: '/experience/ui-design/new' }, { label: 'New', route: undefined });
      analyticsCategory = 'ui-design';
      analyticsAction = 'create';
      break;

    case 'ui-design-project':
      title = 'UI Design Project';
      description = 'View and manage your UI design project';
      breadcrumbs.push({ label: 'UI Design', route: '/experience/ui-design/new' }, { label: 'Project', route: undefined });
      analyticsCategory = 'ui-design';
      analyticsAction = 'view';
      break;

    case 'application-new':
      title = 'New Application';
      description = 'Create a new application with AI assistance';
      breadcrumbs.push({ label: 'Application', route: '/experience/application/new' }, { label: 'New', route: undefined });
      analyticsCategory = 'application';
      analyticsAction = 'create';
      break;

    case 'application-project':
      title = 'Application Project';
      description = 'View and manage your application project';
      breadcrumbs.push({ label: 'Application', route: '/experience/application/new' }, { label: 'Project', route: undefined });
      analyticsCategory = 'application';
      analyticsAction = 'view';
      break;

    case 'project-direct':
      title = 'Project';
      description = 'View project details';
      breadcrumbs.push({ label: 'Project', route: undefined });
      analyticsCategory = 'project';
      analyticsAction = 'view';
      break;

    default:
      breadcrumbs.push({ label: breadcrumbLabel, route: undefined });
      break;
  }

  const metadata: RouteMetadata = {
    title,
    description,
    breadcrumbs,
    analytics: {
      category: analyticsCategory,
      action: analyticsAction,
      label: projectId || undefined
    }
  };

  logger.info('Route metadata resolved:', metadata);
  return of(metadata);
};
